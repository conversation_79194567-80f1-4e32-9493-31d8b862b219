File: types/theme.ts (Lines 1-161)
Type: type
Symbols: Theme related types and interfaces

// Theme System Types
export type ThemeMode = 'light' | 'dark' | 'system';

// Base color structure for both light and dark themes
export interface BaseColors {
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    elevated: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
    inverse: string;
  };
  border: {
    primary: string;
    secondary: string;
    focus: string;
  };
  surface: {
    card: string;
    overlay: string;
    input: string;
  };
}

// Status colors (same for both themes)
export interface StatusColors {
  error: string;
  warning: string;
  success: string;
  info: string;
}

// Primary app color variants
export interface PrimaryColors {
  default: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string;
}

// Journey color structure
export interface JourneyColorVariant {
  default: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string;
}

export interface JourneyColors {
  coral: JourneyColorVariant;
  teal: JourneyColorVariant;
  purple: JourneyColorVariant;
  amber: JourneyColorVariant;
  oceanBlue: JourneyColorVariant;
  magenta: JourneyColorVariant;
  tangerine: JourneyColorVariant;
  evergreen: JourneyColorVariant;
  terraCotta: JourneyColorVariant;
  steelBlue: JourneyColorVariant;
}

// Journey color option for selection
export interface JourneyColorOption {
  id: keyof JourneyColors;
  value: string;
  name: string;
  textColor: string;
}

// Focus state system
export interface FocusRing {
  color: string;
  colorDark: string;
  width: string;
  offset: string;
  style: 'solid' | 'dashed' | 'dotted';
}

export interface FocusAlternatives {
  coral: string;
  white: string;
  dark: string;
}

export interface FocusContrast {
  minimum: number;
  preferred: number;
}

export interface FocusState {
  ring: FocusRing;
  alternatives: FocusAlternatives;
  contrast: FocusContrast;
}

// Opacity system
export interface OpacityLevels {
  subtle: number;  // 0.1
  light: number;   // 0.3
  medium: number;  // 0.5
  high: number;    // 0.8
}

export interface OpacityFunctions {
  hover: (color: string) => string;
  pressed: (color: string) => string;
  disabled: (color: string) => string;
  overlay: (color: string) => string;
}

export interface OpacitySystem extends OpacityLevels {
  functions: OpacityFunctions;
}

// Complete theme structure
export interface Theme {
  mode: ThemeMode;
  colors: BaseColors;
  status: StatusColors;
  primary: PrimaryColors;
  journey: JourneyColors;
  focus: FocusState;
  opacity: OpacitySystem;
}

// Theme context type
export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  isLightMode: boolean;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
  
  // Utility functions
  getFocusStyle: (variant?: keyof FocusAlternatives) => object;
  getFocusVisibleStyle: (variant?: keyof FocusAlternatives) => object;
  getAccessibleTextColor: (backgroundColor: string) => string;
  getJourneyColorVariant: (colorId: keyof JourneyColors, variant: keyof JourneyColorVariant) => string;
  withOpacity: (color: string, opacityLevel: keyof OpacityLevels) => string;
}

// Accessibility matrix entry
export interface AccessibilityEntry {
  ratio: number;
  status: 'AA_PASS' | 'AA_LARGE' | 'FOCUS_PASS' | 'FAIL';
  textColor: string;
  context?: string;
}

export interface AccessibilityMatrix {
  [key: string]: AccessibilityEntry;
}


---

File: contexts/ThemeContext.tsx (Lines 1-151)
Type: component
Symbols: ThemeContext and ThemeProvider

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Theme, ThemeMode, ThemeContextType } from '../types/theme';
import { lightTheme, darkTheme } from '../constants/themes';
import { themeUtils } from '../utils/themeUtils';

const THEME_STORAGE_KEY = 'app_theme_mode_9afaf736';

// Create the context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme Provider Props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme Provider Component
export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [isLoading, setIsLoading] = useState(true);

  // Determine the actual theme based on mode
  const getActualTheme = (mode: ThemeMode): 'light' | 'dark' => {
    if (mode === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return mode;
  };

  const actualTheme = getActualTheme(themeMode);
  const theme: Theme = actualTheme === 'dark' ? darkTheme : lightTheme;
  const isLightMode = actualTheme === 'light';
  const isDarkMode = actualTheme === 'dark';

  // Load saved theme preference on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
          setThemeModeState(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.warn('Failed to load theme preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  // Save theme preference when it changes
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      setThemeModeState(mode);
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  // Toggle between light and dark (ignores system)
  const toggleTheme = () => {
    const newMode = actualTheme === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
  };

  // Utility functions using themeUtils
  const getFocusStyle = (variant?: keyof typeof theme.focus.alternatives) => {
    return themeUtils.getFocusStyle(theme, variant);
  };

  const getFocusVisibleStyle = (variant?: keyof typeof theme.focus.alternatives) => {
    return themeUtils.getFocusVisibleStyle(theme, variant);
  };

  const getAccessibleTextColor = (backgroundColor: string) => {
    return themeUtils.getAccessibleTextColor(backgroundColor);
  };

  const getJourneyColorVariant = (
    colorId: keyof typeof theme.journey,
    variant: keyof typeof theme.journey.coral
  ) => {
    return theme.journey[colorId][variant];
  };

  const withOpacity = (color: string, opacityLevel: keyof typeof theme.opacity) => {
    return themeUtils.withOpacity(color, theme.opacity[opacityLevel]);
  };

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    isLightMode,
    isDarkMode,
    toggleTheme,
    setThemeMode,
    getFocusStyle,
    getFocusVisibleStyle,
    getAccessibleTextColor,
    getJourneyColorVariant,
    withOpacity,
  };

  // Don't render until theme is loaded
  if (isLoading) {
    return null;
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme context
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Convenience hooks
export function useThemeMode() {
  const { themeMode, setThemeMode, toggleTheme } = useTheme();
  return { themeMode, setThemeMode, toggleTheme };
}

export function useThemeColors() {
  const { theme } = useTheme();
  return theme;
}

export function useIsDarkMode() {
  const { isDarkMode } = useTheme();
  return isDarkMode;
}

export function useIsLightMode() {
  const { isLightMode } = useTheme();
  return isLightMode;
}


---

File: constants/themes.ts (Lines 1-328)
Type: unknown
Symbols: lightTheme and darkTheme definitions

import { Theme } from '../types/theme';

// Light Theme Definition
export const lightTheme: Theme = {
  mode: 'light',
  
  // Base colors for light theme
  colors: {
    background: {
      primary: '#FFFFFF',      // Main app background
      secondary: '#F8FAFC',    // Secondary surfaces
      tertiary: '#F1F5F9',     // Subtle backgrounds
      elevated: '#FFFFFF',     // Cards, modals
    },
    text: {
      primary: '#0F172A',      // Main text
      secondary: '#475569',    // Secondary text
      muted: '#94A3B8',        // Subtle text, placeholders
      inverse: '#FFFFFF',      // Text on dark backgrounds
    },
    border: {
      primary: '#E2E8F0',      // Main borders
      secondary: '#CBD5E1',    // Subtle borders
      focus: '#e54b50',        // Focus rings (refined coral)
    },
    surface: {
      card: '#FFFFFF',         // Card backgrounds
      overlay: 'rgba(15, 23, 42, 0.8)', // Modal overlays
      input: '#F8FAFC',        // Input backgrounds
    },
  },
  
  // Status colors (same for both themes)
  status: {
    error: '#EF4444',        // Error states
    warning: '#F59E0B',      // Warning states
    success: '#10B981',      // Success states
    info: '#3B82F6',         // Info states
  },
  
  // Primary app color (refined coral)
  primary: {
    default: '#e54b50',      // Improved contrast (4.7:1 ratio)
    light: '#fc6267',        // Original coral as lighter variant
    dark: '#cc3a3f',         // Darker for enhanced contrast
    muted: '#fde2e4',        // Subtle backgrounds
    contrast: '#FFFFFF',     // Always white text on coral
  },
  
  // Journey colors with accessibility improvements
  journey: {
    coral: {
      default: '#e54b50',
      light: '#fc6267',
      dark: '#cc3a3f',
      muted: '#fde2e4',
      contrast: '#FFFFFF',
    },
    teal: {
      default: '#30CFC5',
      light: '#5dd9d1',
      dark: '#26a69a',
      muted: '#e0f7f5',
      contrast: '#FFFFFF',
    },
    purple: {
      default: '#8579ED',
      light: '#a396f0',
      dark: '#6b5ce6',
      muted: '#ede9fe',
      contrast: '#FFFFFF',
    },
    amber: {
      default: '#F59E0B',      // Improved from #FFBA45 (4.6:1 ratio)
      light: '#FFBA45',        // Original as light variant
      dark: '#D97706',         // Darker for better contrast
      muted: '#fef3c7',
      contrast: '#000000',     // Always black text on amber
    },
    oceanBlue: {
      default: '#2E9FE3',
      light: '#5bb2e8',
      dark: '#1976d2',
      muted: '#dbeafe',
      contrast: '#FFFFFF',
    },
    magenta: {
      default: '#E9608A',
      light: '#ed7ea1',
      dark: '#d1477a',
      muted: '#fce7f3',
      contrast: '#FFFFFF',
    },
    tangerine: {
      default: '#E85D0F',      // Improved to reach 4.5:1 ratio
      light: '#FF9D5C',        // Original as light variant
      dark: '#D97706',
      muted: '#fed7aa',
      contrast: '#000000',     // Always black text on tangerine
    },
    evergreen: {
      default: '#1E8449',
      light: '#22c55e',
      dark: '#166534',
      muted: '#dcfce7',
      contrast: '#FFFFFF',
    },
    terraCotta: {
      default: '#E27D60',
      light: '#e89982',
      dark: '#dc6545',
      muted: '#fed7d7',
      contrast: '#FFFFFF',
    },
    steelBlue: {
      default: '#4682B4',
      light: '#6b9dc7',
      dark: '#2563eb',
      muted: '#dbeafe',
      contrast: '#FFFFFF',
    },
  },
  
  // Focus state system
  focus: {
    ring: {
      color: '#2563EB',        // High-contrast blue for visibility
      colorDark: '#60A5FA',    // Lighter blue for dark theme
      width: '2px',
      offset: '2px',
      style: 'solid',
    },
    alternatives: {
      coral: '#e54b50',        // Primary color focus
      white: '#FFFFFF',        // For dark backgrounds
      dark: '#1F2937',         // For light backgrounds
    },
    contrast: {
      minimum: 3.0,            // WCAG requirement
      preferred: 4.5,          // Preferred for better visibility
    },
  },
  
  // Opacity system
  opacity: {
    subtle: 0.1,             // 10% - Very light tints
    light: 0.3,              // 30% - Subtle highlights
    medium: 0.5,             // 50% - Overlays, disabled
    high: 0.8,               // 80% - Semi-transparent
    functions: {
      hover: (color: string) => `${color}CC`,      // 80% opacity
      pressed: (color: string) => `${color}E6`,    // 90% opacity
      disabled: (color: string) => `${color}80`,   // 50% opacity
      overlay: (color: string) => `${color}33`,    // 20% opacity
    },
  },
};

// Dark Theme Definition
export const darkTheme: Theme = {
  mode: 'dark',
  
  // Base colors for dark theme
  colors: {
    background: {
      primary: '#0F172A',      // Main app background
      secondary: '#1E293B',    // Secondary surfaces
      tertiary: '#334155',     // Subtle backgrounds
      elevated: '#1E293B',     // Cards, modals
    },
    text: {
      primary: '#F8FAFC',      // Main text
      secondary: '#CBD5E1',    // Secondary text
      muted: '#64748B',        // Subtle text, placeholders
      inverse: '#0F172A',      // Text on light backgrounds
    },
    border: {
      primary: '#334155',      // Main borders
      secondary: '#475569',    // Subtle borders
      focus: '#fd8a8e',        // Focus rings (lighter coral for dark)
    },
    surface: {
      card: '#1E293B',         // Card backgrounds
      overlay: 'rgba(0, 0, 0, 0.8)', // Modal overlays
      input: '#334155',        // Input backgrounds
    },
  },
  
  // Status colors (same as light theme)
  status: {
    error: '#EF4444',
    warning: '#F59E0B',
    success: '#10B981',
    info: '#3B82F6',
  },
  
  // Primary app color adjusted for dark theme
  primary: {
    default: '#fd8a8e',      // Slightly lighter for dark backgrounds
    light: '#fc6267',        // Original coral
    dark: '#e5424a',         // Darker variant
    muted: '#4a1a1c',        // Darker muted for dark theme
    contrast: '#FFFFFF',     // White text
  },
  
  // Journey colors adjusted for dark theme
  journey: {
    coral: {
      default: '#fd8a8e',      // Lighter for dark backgrounds
      light: '#fc6267',
      dark: '#e5424a',
      muted: '#4a1a1c',
      contrast: '#FFFFFF',
    },
    teal: {
      default: '#5dd9d1',      // Brighter for visibility
      light: '#30CFC5',
      dark: '#26a69a',
      muted: '#1a3a38',
      contrast: '#FFFFFF',
    },
    purple: {
      default: '#a396f0',      // Lighter for dark backgrounds
      light: '#8579ED',
      dark: '#6b5ce6',
      muted: '#2d2a4a',
      contrast: '#FFFFFF',
    },
    amber: {
      default: '#F59E0B',      // More saturated for dark backgrounds
      light: '#FBBF24',
      dark: '#D97706',
      muted: '#3a2a0a',
      contrast: '#000000',
    },
    oceanBlue: {
      default: '#5bb2e8',      // Brighter for visibility
      light: '#2E9FE3',
      dark: '#1976d2',
      muted: '#1a2e3a',
      contrast: '#FFFFFF',
    },
    magenta: {
      default: '#ed7ea1',      // Lighter for dark backgrounds
      light: '#E9608A',
      dark: '#d1477a',
      muted: '#3a1a2a',
      contrast: '#FFFFFF',
    },
    tangerine: {
      default: '#FB923C',      // Enhanced visibility for dark
      light: '#FF9D5C',
      dark: '#EA580C',
      muted: '#3a1f0a',
      contrast: '#000000',
    },
    evergreen: {
      default: '#22c55e',      // Brighter for visibility
      light: '#1E8449',
      dark: '#166534',
      muted: '#1a3a2a',
      contrast: '#FFFFFF',
    },
    terraCotta: {
      default: '#e89982',      // Lighter for dark backgrounds
      light: '#E27D60',
      dark: '#dc6545',
      muted: '#3a2a1a',
      contrast: '#FFFFFF',
    },
    steelBlue: {
      default: '#6b9dc7',      // Lighter for dark backgrounds
      light: '#4682B4',
      dark: '#2563eb',
      muted: '#1a2a3a',
      contrast: '#FFFFFF',
    },
  },
  
  // Focus state adjusted for dark theme
  focus: {
    ring: {
      color: '#60A5FA',        // Lighter blue for dark backgrounds
      colorDark: '#60A5FA',    // Same for consistency
      width: '2px',
      offset: '2px',
      style: 'solid',
    },
    alternatives: {
      coral: '#fd8a8e',        // Lighter coral for dark backgrounds
      white: '#F8FAFC',        // Slightly off-white
      dark: '#374151',         // Adjusted for dark theme
    },
    contrast: {
      minimum: 3.0,
      preferred: 4.5,
    },
  },
  
  // Opacity system (same as light theme)
  opacity: {
    subtle: 0.1,
    light: 0.3,
    medium: 0.5,
    high: 0.8,
    functions: {
      hover: (color: string) => `${color}CC`,
      pressed: (color: string) => `${color}E6`,
      disabled: (color: string) => `${color}80`,
      overlay: (color: string) => `${color}33`,
    },
  },
};

// Journey color options for selection
export const journeyColorOptions = [
  { id: 'coral' as const, value: '#e54b50', name: 'Coral', textColor: '#FFFFFF' },
  { id: 'teal' as const, value: '#30CFC5', name: 'Teal', textColor: '#FFFFFF' },
  { id: 'purple' as const, value: '#8579ED', name: 'Purple', textColor: '#FFFFFF' },
  { id: 'amber' as const, value: '#F59E0B', name: 'Amber', textColor: '#000000' },
  { id: 'oceanBlue' as const, value: '#2E9FE3', name: 'Ocean Blue', textColor: '#FFFFFF' },
  { id: 'magenta' as const, value: '#E9608A', name: 'Magenta', textColor: '#FFFFFF' },
  { id: 'tangerine' as const, value: '#E85D0F', name: 'Tangerine', textColor: '#000000' },
  { id: 'evergreen' as const, value: '#1E8449', name: 'Evergreen', textColor: '#FFFFFF' },
  { id: 'terraCotta' as const, value: '#E27D60', name: 'Terra Cotta', textColor: '#FFFFFF' },
  { id: 'steelBlue' as const, value: '#4682B4', name: 'Steel Blue', textColor: '#FFFFFF' },
];


---

File: components/examples/ThemeShowcase.tsx (Lines 1-314)
Type: component
Symbols: ThemeShowcase

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { JourneyColors } from '../../types/theme';
import { Heart, Star, Settings, User } from 'lucide-react-native';
import ThemeSelector from '../ui/ThemeSelector';

export function ThemeShowcase() {
  const { theme, getJourneyColorVariant, withOpacity, getFocusStyle } = useTheme();
  const [inputValue, setInputValue] = useState('');
  const [selectedJourneyColor, setSelectedJourneyColor] = useState<keyof JourneyColors>('coral');

  const journeyColorOptions: { id: keyof JourneyColors; name: string }[] = [
    { id: 'coral', name: 'Coral' },
    { id: 'teal', name: 'Teal' },
    { id: 'purple', name: 'Purple' },
    { id: 'amber', name: 'Amber' },
    { id: 'oceanBlue', name: 'Ocean Blue' },
    { id: 'magenta', name: 'Magenta' },
    { id: 'tangerine', name: 'Tangerine' },
    { id: 'evergreen', name: 'Evergreen' },
    { id: 'terraCotta', name: 'Terra Cotta' },
    { id: 'steelBlue', name: 'Steel Blue' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    scrollContent: {
      padding: 20,
      gap: 24,
    },
    section: {
      gap: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 8,
    },
    card: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    colorSwatch: {
      width: 60,
      height: 60,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'transparent',
    },
    selectedColorSwatch: {
      borderColor: theme.colors.border.focus,
    },
    colorLabel: {
      fontSize: 10,
      fontWeight: '500',
      textAlign: 'center',
      marginTop: 2,
    },
    buttonRow: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
    },
    primaryButton: {
      backgroundColor: theme.primary.default,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8,
      flex: 1,
      minWidth: 120,
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.border.primary,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8,
      flex: 1,
      minWidth: 120,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '500',
    },
    primaryButtonText: {
      color: theme.primary.contrast,
    },
    secondaryButtonText: {
      color: theme.colors.text.primary,
    },
    input: {
      backgroundColor: theme.colors.surface.input,
      borderWidth: 1,
      borderColor: theme.colors.border.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text.primary,
    },
    statusRow: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      minWidth: 80,
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
    },
    textSample: {
      gap: 8,
    },
    primaryText: {
      fontSize: 16,
      color: theme.colors.text.primary,
      fontWeight: '500',
    },
    secondaryText: {
      fontSize: 14,
      color: theme.colors.text.secondary,
    },
    mutedText: {
      fontSize: 12,
      color: theme.colors.text.muted,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Theme Selector */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Theme Selector</Text>
          <View style={styles.card}>
            <ThemeSelector variant="horizontal" showLabels={true} size="medium" />
          </View>
        </View>

        {/* Text Samples */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Typography</Text>
          <View style={[styles.card, styles.textSample]}>
            <Text style={styles.primaryText}>Primary Text - Main content and headings</Text>
            <Text style={styles.secondaryText}>Secondary Text - Supporting information</Text>
            <Text style={styles.mutedText}>Muted Text - Subtle details and placeholders</Text>
          </View>
        </View>

        {/* Buttons */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Buttons</Text>
          <View style={styles.card}>
            <View style={styles.buttonRow}>
              <TouchableOpacity style={styles.primaryButton}>
                <Heart size={16} color={theme.primary.contrast} />
                <Text style={[styles.buttonText, styles.primaryButtonText]}>Primary</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.secondaryButton}>
                <Star size={16} color={theme.colors.text.primary} />
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>Secondary</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Input Field</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="Type something here..."
              placeholderTextColor={theme.colors.text.muted}
              value={inputValue}
              onChangeText={setInputValue}
            />
          </View>
        </View>

        {/* Status Colors */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status Colors</Text>
          <View style={styles.card}>
            <View style={styles.statusRow}>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.success, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.success }]}>Success</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.warning, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.warning }]}>Warning</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.error, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.error }]}>Error</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.info, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.info }]}>Info</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Journey Colors */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Journey Colors</Text>
          <View style={styles.card}>
            <View style={styles.colorGrid}>
              {journeyColorOptions.map(({ id, name }) => {
                const color = getJourneyColorVariant(id, 'default');
                const isSelected = selectedJourneyColor === id;
                
                return (
                  <TouchableOpacity
                    key={id}
                    onPress={() => setSelectedJourneyColor(id)}
                    accessibilityRole="button"
                    accessibilityLabel={`Select ${name} color`}
                  >
                    <View
                      style={[
                        styles.colorSwatch,
                        { backgroundColor: color },
                        isSelected && styles.selectedColorSwatch,
                      ]}
                    >
                      <Text
                        style={[
                          styles.colorLabel,
                          { color: getJourneyColorVariant(id, 'contrast') },
                        ]}
                      >
                        {name}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </View>

        {/* Selected Journey Color Variants */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{journeyColorOptions.find(c => c.id === selectedJourneyColor)?.name} Variants</Text>
          <View style={styles.card}>
            <View style={styles.colorGrid}>
              {(['default', 'light', 'dark', 'muted'] as const).map((variant) => {
                const color = getJourneyColorVariant(selectedJourneyColor, variant);
                
                return (
                  <View key={variant}>
                    <View
                      style={[
                        styles.colorSwatch,
                        { backgroundColor: color },
                      ]}
                    >
                      <Text
                        style={[
                          styles.colorLabel,
                          { color: getJourneyColorVariant(selectedJourneyColor, 'contrast') },
                        ]}
                      >
                        {variant}
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

export default ThemeShowcase;

---

File: constants/colors.ts (Lines 2-46)
Type: unknown
Symbols: COLORS and JOURNEY_COLORS

export const COLORS = {
  // Primary palette
  primary: '#2196F3',
  success: '#10B981',
  warning: '#F59E0B', 
  error: '#EF4444',
  
  // Journey colors
  blue: '#2196F3',
  green: '#10B981',
  purple: '#8B5CF6',
  orange: '#F97316',
  red: '#EF4444',
  teal: '#14B8A6',
  pink: '#EC4899',
  indigo: '#6366F1',
  amber: '#F59E0B',
  gray: '#6B7280',
  
  // Gray scale
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB', 
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray700: '#374151',
  gray900: '#1F2937',
  
  // Background
  background: '#FFFFFF',
  surface: '#F9FAFB'
};

export const JOURNEY_COLORS = [
  COLORS.blue,
  COLORS.green, 
  COLORS.purple,
  COLORS.orange,
  COLORS.red,
  COLORS.teal,
  COLORS.pink,
  COLORS.indigo,
  COLORS.amber,
  COLORS.gray
];

---

File: utils/themeUtils.ts (Lines 1-216)
Type: util
Symbols: themeUtils

import { Theme } from '../types/theme';

/**
 * Theme utility functions for color manipulation and accessibility
 */
export const themeUtils = {
  /**
   * Convert hex color to RGB values
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  /**
   * Calculate luminance of a color (for contrast calculations)
   */
  getLuminance(hex: string): number {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return 0;

    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getLuminance(color1);
    const lum2 = this.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Check if a color combination meets WCAG AA standards
   */
  meetsWCAG(backgroundColor: string, textColor: string, isLargeText = false): boolean {
    const ratio = this.getContrastRatio(backgroundColor, textColor);
    return isLargeText ? ratio >= 3.0 : ratio >= 4.5;
  },

  /**
   * Get accessible text color (black or white) for a given background
   */
  getAccessibleTextColor(backgroundColor: string): string {
    const whiteRatio = this.getContrastRatio(backgroundColor, '#FFFFFF');
    const blackRatio = this.getContrastRatio(backgroundColor, '#000000');
    return whiteRatio > blackRatio ? '#FFFFFF' : '#000000';
  },

  /**
   * Add opacity to a hex color
   */
  withOpacity(hex: string, opacity: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;
    
    const { r, g, b } = rgb;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  /**
   * Lighten a color by a percentage
   */
  lightenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = percent / 100;
    
    const newR = Math.min(255, Math.round(r + (255 - r) * amount));
    const newG = Math.min(255, Math.round(g + (255 - g) * amount));
    const newB = Math.min(255, Math.round(b + (255 - b) * amount));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  },

  /**
   * Darken a color by a percentage
   */
  darkenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = percent / 100;
    
    const newR = Math.max(0, Math.round(r * (1 - amount)));
    const newG = Math.max(0, Math.round(g * (1 - amount)));
    const newB = Math.max(0, Math.round(b * (1 - amount)));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  },

  /**
   * Generate focus style object
   */
  getFocusStyle(theme: Theme, variant?: keyof typeof theme.focus.alternatives): object {
    const isDark = theme.mode === 'dark';
    const ringColor = variant 
      ? theme.focus.alternatives[variant]
      : (isDark ? theme.focus.ring.colorDark : theme.focus.ring.color);
    
    return {
      outline: 'none',
      shadowColor: ringColor,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 1,
      shadowRadius: parseInt(theme.focus.ring.width),
      elevation: 0, // Android
    };
  },

  /**
   * Generate focus-visible style (keyboard only)
   */
  getFocusVisibleStyle(theme: Theme, variant?: keyof typeof theme.focus.alternatives): object {
    return {
      // This would be handled by the component's focus state
      // React Native doesn't have :focus-visible, so components need to track keyboard vs touch
      ...this.getFocusStyle(theme, variant),
    };
  },

  /**
   * Generate button styles with theme
   */
  getButtonStyles(theme: Theme, variant: 'primary' | 'secondary' = 'primary') {
    if (variant === 'primary') {
      return {
        backgroundColor: theme.primary.default,
        color: theme.primary.contrast,
        borderColor: 'transparent',
      };
    }
    
    return {
      backgroundColor: 'transparent',
      color: theme.colors.text.primary,
      borderColor: theme.colors.border.primary,
      borderWidth: 1,
    };
  },

  /**
   * Generate input styles with theme
   */
  getInputStyles(theme: Theme) {
    return {
      backgroundColor: theme.colors.surface.input,
      borderColor: theme.colors.border.primary,
      color: theme.colors.text.primary,
      placeholderTextColor: theme.colors.text.muted,
    };
  },

  /**
   * Generate card styles with theme
   */
  getCardStyles(theme: Theme) {
    return {
      backgroundColor: theme.colors.surface.card,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.mode === 'dark' ? '#000000' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: theme.mode === 'dark' ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 2, // Android
    };
  },

  /**
   * Validate theme accessibility
   */
  validateThemeAccessibility(theme: Theme): { [key: string]: boolean } {
    const results: { [key: string]: boolean } = {};
    
    // Check primary color accessibility
    results.primaryOnBackground = this.meetsWCAG(
      theme.colors.background.primary,
      theme.primary.default
    );
    
    results.textOnBackground = this.meetsWCAG(
      theme.colors.background.primary,
      theme.colors.text.primary
    );
    
    results.textOnPrimary = this.meetsWCAG(
      theme.primary.default,
      theme.primary.contrast
    );
    
    // Check focus ring accessibility
    results.focusOnBackground = this.getContrastRatio(
      theme.colors.background.primary,
      theme.focus.ring.color
    ) >= 3.0;
    
    return results;
  },
};


---

File: components/ui/ThemeSelector.tsx (Lines 1-105)
Type: component
Symbols: ThemeSelector

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme, useThemeMode } from '../../contexts/ThemeContext';
import { ThemeMode } from '../../types/theme';
import { Sun, Moon, Smartphone } from 'lucide-react-native';

interface ThemeSelectorProps {
  variant?: 'horizontal' | 'vertical';
  showLabels?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export function ThemeSelector({ 
  variant = 'horizontal', 
  showLabels = true, 
  size = 'medium' 
}: ThemeSelectorProps) {
  const { theme } = useTheme();
  const { themeMode, setThemeMode } = useThemeMode();

  const iconSize = size === 'small' ? 16 : size === 'medium' ? 20 : 24;
  const buttonSize = size === 'small' ? 36 : size === 'medium' ? 44 : 52;

  const themeOptions: { mode: ThemeMode; icon: any; label: string }[] = [
    { mode: 'light', icon: Sun, label: 'Light' },
    { mode: 'dark', icon: Moon, label: 'Dark' },
    { mode: 'system', icon: Smartphone, label: 'System' },
  ];

  const styles = StyleSheet.create({
    container: {
      flexDirection: variant === 'horizontal' ? 'row' : 'column',
      alignItems: 'center',
      gap: 8,
    },
    optionContainer: {
      alignItems: 'center',
      gap: 4,
    },
    button: {
      width: buttonSize,
      height: buttonSize,
      borderRadius: buttonSize / 2,
      backgroundColor: theme.colors.surface.card,
      borderWidth: 2,
      borderColor: 'transparent',
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    activeButton: {
      backgroundColor: theme.primary.default,
      borderColor: theme.primary.dark,
    },
    label: {
      fontSize: size === 'small' ? 12 : size === 'medium' ? 14 : 16,
      fontWeight: '500',
      color: theme.colors.text.secondary,
    },
    activeLabel: {
      color: theme.colors.text.primary,
      fontWeight: '600',
    },
  });

  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  return (
    <View style={styles.container}>
      {themeOptions.map(({ mode, icon: Icon, label }) => {
        const isActive = themeMode === mode;
        
        return (
          <View key={mode} style={styles.optionContainer}>
            <TouchableOpacity
              style={[styles.button, isActive && styles.activeButton]}
              onPress={() => handleThemeChange(mode)}
              accessibilityRole="button"
              accessibilityLabel={`Switch to ${label} theme`}
              accessibilityState={{ selected: isActive }}
            >
              <Icon
                size={iconSize}
                color={isActive ? theme.primary.contrast : theme.colors.text.primary}
              />
            </TouchableOpacity>
            {showLabels && (
              <Text style={[styles.label, isActive && styles.activeLabel]}>
                {label}
              </Text>
            )}
          </View>
        );
      })}
    </View>
  );
}

export default ThemeSelector;

---

File: constants/index.ts (Lines 2-43)
Type: unknown
Symbols: STORAGE_KEYS, journeyColorOptions, APP_CONFIG and re-exports

export const STORAGE_KEYS = {
  THEME: 'app_theme_mode_9afaf736',
  USER_PREFERENCES: 'user_preferences_9afaf736',
  JOURNEY_DATA: 'journey_data_9afaf736',
  JOURNEY_STORE: 'journey_store_9afaf736',
  WAYPOINT_STORE: 'waypoint_store_9afaf736',
  AUTH_TOKEN: 'auth_token_9afaf736',
  AUTH_STORE: 'auth_store_9afaf736',
  APP_STORE: 'app_store_9afaf736',
  FIRST_LAUNCH: 'first_launch_9afaf736',
  LAST_ACTIVE_JOURNEY: 'last_active_journey_9afaf736',
};

// Re-export theme system
export { lightTheme, darkTheme } from './themes';
export { COLORS, JOURNEY_COLORS } from './colors';

// Journey color options for UI selection
export const journeyColorOptions = [
  { id: 'coral', name: 'Coral', value: '#e54b50', textColor: '#FFFFFF' },
  { id: 'teal', name: 'Teal', value: '#30CFC5', textColor: '#FFFFFF' },
  { id: 'purple', name: 'Purple', value: '#8579ED', textColor: '#FFFFFF' },
  { id: 'amber', name: 'Amber', value: '#F59E0B', textColor: '#000000' },
  { id: 'oceanBlue', name: 'Ocean Blue', value: '#2E9FE3', textColor: '#FFFFFF' },
  { id: 'magenta', name: 'Magenta', value: '#E9608A', textColor: '#FFFFFF' },
  { id: 'tangerine', name: 'Tangerine', value: '#E85D0F', textColor: '#000000' },
  { id: 'evergreen', name: 'Evergreen', value: '#1E8449', textColor: '#FFFFFF' },
  { id: 'terraCotta', name: 'Terra Cotta', value: '#E27D60', textColor: '#FFFFFF' },
  { id: 'steelBlue', name: 'Steel Blue', value: '#4682B4', textColor: '#FFFFFF' },
] as const;

// App constants
export const APP_CONFIG = {
  PROJECT_ID: '9afaf736-bdc4-4ad5-bbc8-0dc37ac008ed',
  VERSION: '1.0.0',
  BUILD: '1',
};

// Theme-related exports
export type { Theme, ThemeMode, ThemeContextType } from '../types/theme';
export { useTheme, useThemeMode, useThemeColors, useIsDarkMode, useIsLightMode } from '../contexts/ThemeContext';
export { themeUtils } from '../utils/themeUtils';

---

File: screens/ThemeDemo.tsx (Lines 1-63)
Type: component
Symbols: ThemeDemo

import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useThemeStyles } from '../hooks/useThemeStyles';
import ThemeShowcase from '../components/examples/ThemeShowcase';
import { Palette } from 'lucide-react-native';

export function ThemeDemo() {
  const { theme } = useTheme();
  const { styles: themeStyles } = useThemeStyles();

  const styles = StyleSheet.create({
    header: {
      backgroundColor: theme.colors.background.secondary,
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text.primary,
    },
    headerSubtitle: {
      fontSize: 14,
      color: theme.colors.text.secondary,
      marginTop: 2,
    },
  });

  return (
    <SafeAreaView style={themeStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={{
            backgroundColor: theme.primary.default,
            padding: 8,
            borderRadius: 8,
          }}>
            <Palette size={20} color={theme.primary.contrast} />
          </View>
          <View>
            <Text style={styles.headerTitle}>Theme System Demo</Text>
            <Text style={styles.headerSubtitle}>Comprehensive theme showcase</Text>
          </View>
        </View>
      </View>

      {/* Content */}
      <ThemeShowcase />
    </SafeAreaView>
  );
}

export default ThemeDemo;

---

File: App.tsx (Lines 2-52)
Type: component
Symbols: App and AppContent

import React from 'react';
import { DarkTheme, DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Toaster } from 'sonner-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import MainNavigator from './navigation';

// App Content Component (needs to be inside ThemeProvider)
function AppContent() {
    const { isDarkMode, theme } = useTheme();

    // Create navigation theme based on our theme system
    const navigationTheme = {
        ...(isDarkMode ? DarkTheme : DefaultTheme),
        colors: {
            ...(isDarkMode ? DarkTheme.colors : DefaultTheme.colors),
            primary: theme.primary.default,
            background: theme.colors.background.primary,
            card: theme.colors.surface.card,
            text: theme.colors.text.primary,
            border: theme.colors.border.primary,
            notification: theme.status.error,
        },
    };

    return (
        <SafeAreaProvider>
            <NavigationContainer theme={navigationTheme}>
                <StatusBar
                    style={isDarkMode ? 'light' : 'dark'}
                    backgroundColor={theme.colors.background.primary}
                />
                <Toaster 
                    theme={isDarkMode ? 'dark' : 'light'} 
                    richColors 
                />
                <MainNavigator />
            </NavigationContainer>
        </SafeAreaProvider>
    );
}

// Main App Component
export default function App() {
    return (
        <ThemeProvider>
            <AppContent />
        </ThemeProvider>
    );
}

