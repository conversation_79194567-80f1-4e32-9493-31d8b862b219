import { Theme } from '../types/theme';

// Light Theme Definition
export const lightTheme: Theme = {
  mode: 'light',
  
  // Base colors for light theme
  colors: {
    background: {
      primary: '#FFFFFF',      // Main app background
      secondary: '#F8FAFC',    // Secondary surfaces
      tertiary: '#F1F5F9',     // Subtle backgrounds
      elevated: '#FFFFFF',     // Cards, modals
    },
    text: {
      primary: '#0F172A',      // Main text
      secondary: '#475569',    // Secondary text
      muted: '#94A3B8',        // Subtle text, placeholders
      inverse: '#FFFFFF',      // Text on dark backgrounds
    },
    border: {
      primary: '#E2E8F0',      // Main borders
      secondary: '#CBD5E1',    // Subtle borders
      focus: '#e54b50',        // Focus rings (refined coral)
    },
    surface: {
      card: '#FFFFFF',         // Card backgrounds
      overlay: 'rgba(15, 23, 42, 0.8)', // Modal overlays
      input: '#F8FAFC',        // Input backgrounds
    },
  },
  
  // Status colors (same for both themes)
  status: {
    error: '#EF4444',        // Error states
    warning: '#F59E0B',      // Warning states
    success: '#10B981',      // Success states
    info: '#3B82F6',         // Info states
  },
  
  // Primary app color (refined coral)
  primary: {
    default: '#e54b50',      // Improved contrast (4.7:1 ratio)
    light: '#fc6267',        // Original coral as lighter variant
    dark: '#cc3a3f',         // Darker for enhanced contrast
    muted: '#fde2e4',        // Subtle backgrounds
    contrast: '#FFFFFF',     // Always white text on coral
  },
  
  // Journey colors with accessibility improvements
  journey: {
    coral: {
      default: '#e54b50',
      light: '#fc6267',
      dark: '#cc3a3f',
      muted: '#fde2e4',
      contrast: '#FFFFFF',
    },
    teal: {
      default: '#30CFC5',
      light: '#5dd9d1',
      dark: '#26a69a',
      muted: '#e0f7f5',
      contrast: '#FFFFFF',
    },
    purple: {
      default: '#8579ED',
      light: '#a396f0',
      dark: '#6b5ce6',
      muted: '#ede9fe',
      contrast: '#FFFFFF',
    },
    amber: {
      default: '#F59E0B',      // Improved from #FFBA45 (4.6:1 ratio)
      light: '#FFBA45',        // Original as light variant
      dark: '#D97706',         // Darker for better contrast
      muted: '#fef3c7',
      contrast: '#000000',     // Always black text on amber
    },
    oceanBlue: {
      default: '#2E9FE3',
      light: '#5bb2e8',
      dark: '#1976d2',
      muted: '#dbeafe',
      contrast: '#FFFFFF',
    },
    magenta: {
      default: '#E9608A',
      light: '#ed7ea1',
      dark: '#d1477a',
      muted: '#fce7f3',
      contrast: '#FFFFFF',
    },
    tangerine: {
      default: '#E85D0F',      // Improved to reach 4.5:1 ratio
      light: '#FF9D5C',        // Original as light variant
      dark: '#D97706',
      muted: '#fed7aa',
      contrast: '#000000',     // Always black text on tangerine
    },
    evergreen: {
      default: '#1E8449',
      light: '#22c55e',
      dark: '#166534',
      muted: '#dcfce7',
      contrast: '#FFFFFF',
    },
    terraCotta: {
      default: '#E27D60',
      light: '#e89982',
      dark: '#dc6545',
      muted: '#fed7d7',
      contrast: '#FFFFFF',
    },
    steelBlue: {
      default: '#4682B4',
      light: '#6b9dc7',
      dark: '#2563eb',
      muted: '#dbeafe',
      contrast: '#FFFFFF',
    },
  },
  
  // Focus state system
  focus: {
    ring: {
      color: '#2563EB',        // High-contrast blue for visibility
      colorDark: '#60A5FA',    // Lighter blue for dark theme
      width: '2px',
      offset: '2px',
      style: 'solid',
    },
    alternatives: {
      coral: '#e54b50',        // Primary color focus
      white: '#FFFFFF',        // For dark backgrounds
      dark: '#1F2937',         // For light backgrounds
    },
    contrast: {
      minimum: 3.0,            // WCAG requirement
      preferred: 4.5,          // Preferred for better visibility
    },
  },
  
  // Opacity system
  opacity: {
    subtle: 0.1,             // 10% - Very light tints
    light: 0.3,              // 30% - Subtle highlights
    medium: 0.5,             // 50% - Overlays, disabled
    high: 0.8,               // 80% - Semi-transparent
    functions: {
      hover: (color: string) => `${color}CC`,      // 80% opacity
      pressed: (color: string) => `${color}E6`,    // 90% opacity
      disabled: (color: string) => `${color}80`,   // 50% opacity
      overlay: (color: string) => `${color}33`,    // 20% opacity
    },
  },
};

// Dark Theme Definition
export const darkTheme: Theme = {
  mode: 'dark',
  
  // Base colors for dark theme
  colors: {
    background: {
      primary: '#0F172A',      // Main app background
      secondary: '#1E293B',    // Secondary surfaces
      tertiary: '#334155',     // Subtle backgrounds
      elevated: '#1E293B',     // Cards, modals
    },
    text: {
      primary: '#F8FAFC',      // Main text
      secondary: '#CBD5E1',    // Secondary text
      muted: '#64748B',        // Subtle text, placeholders
      inverse: '#0F172A',      // Text on light backgrounds
    },
    border: {
      primary: '#334155',      // Main borders
      secondary: '#475569',    // Subtle borders
      focus: '#fd8a8e',        // Focus rings (lighter coral for dark)
    },
    surface: {
      card: '#1E293B',         // Card backgrounds
      overlay: 'rgba(0, 0, 0, 0.8)', // Modal overlays
      input: '#334155',        // Input backgrounds
    },
  },
  
  // Status colors (same as light theme)
  status: {
    error: '#EF4444',
    warning: '#F59E0B',
    success: '#10B981',
    info: '#3B82F6',
  },
  
  // Primary app color adjusted for dark theme
  primary: {
    default: '#fd8a8e',      // Slightly lighter for dark backgrounds
    light: '#fc6267',        // Original coral
    dark: '#e5424a',         // Darker variant
    muted: '#4a1a1c',        // Darker muted for dark theme
    contrast: '#FFFFFF',     // White text
  },
  
  // Journey colors adjusted for dark theme
  journey: {
    coral: {
      default: '#fd8a8e',      // Lighter for dark backgrounds
      light: '#fc6267',
      dark: '#e5424a',
      muted: '#4a1a1c',
      contrast: '#FFFFFF',
    },
    teal: {
      default: '#5dd9d1',      // Brighter for visibility
      light: '#30CFC5',
      dark: '#26a69a',
      muted: '#1a3a38',
      contrast: '#FFFFFF',
    },
    purple: {
      default: '#a396f0',      // Lighter for dark backgrounds
      light: '#8579ED',
      dark: '#6b5ce6',
      muted: '#2d2a4a',
      contrast: '#FFFFFF',
    },
    amber: {
      default: '#F59E0B',      // More saturated for dark backgrounds
      light: '#FBBF24',
      dark: '#D97706',
      muted: '#3a2a0a',
      contrast: '#000000',
    },
    oceanBlue: {
      default: '#5bb2e8',      // Brighter for visibility
      light: '#2E9FE3',
      dark: '#1976d2',
      muted: '#1a2e3a',
      contrast: '#FFFFFF',
    },
    magenta: {
      default: '#ed7ea1',      // Lighter for dark backgrounds
      light: '#E9608A',
      dark: '#d1477a',
      muted: '#3a1a2a',
      contrast: '#FFFFFF',
    },
    tangerine: {
      default: '#FB923C',      // Enhanced visibility for dark
      light: '#FF9D5C',
      dark: '#EA580C',
      muted: '#3a1f0a',
      contrast: '#000000',
    },
    evergreen: {
      default: '#22c55e',      // Brighter for visibility
      light: '#1E8449',
      dark: '#166534',
      muted: '#1a3a2a',
      contrast: '#FFFFFF',
    },
    terraCotta: {
      default: '#e89982',      // Lighter for dark backgrounds
      light: '#E27D60',
      dark: '#dc6545',
      muted: '#3a2a1a',
      contrast: '#FFFFFF',
    },
    steelBlue: {
      default: '#6b9dc7',      // Lighter for dark backgrounds
      light: '#4682B4',
      dark: '#2563eb',
      muted: '#1a2a3a',
      contrast: '#FFFFFF',
    },
  },
  
  // Focus state adjusted for dark theme
  focus: {
    ring: {
      color: '#60A5FA',        // Lighter blue for dark backgrounds
      colorDark: '#60A5FA',    // Same for consistency
      width: '2px',
      offset: '2px',
      style: 'solid',
    },
    alternatives: {
      coral: '#fd8a8e',        // Lighter coral for dark backgrounds
      white: '#F8FAFC',        // Slightly off-white
      dark: '#374151',         // Adjusted for dark theme
    },
    contrast: {
      minimum: 3.0,
      preferred: 4.5,
    },
  },
  
  // Opacity system (same as light theme)
  opacity: {
    subtle: 0.1,
    light: 0.3,
    medium: 0.5,
    high: 0.8,
    functions: {
      hover: (color: string) => `${color}CC`,
      pressed: (color: string) => `${color}E6`,
      disabled: (color: string) => `${color}80`,
      overlay: (color: string) => `${color}33`,
    },
  },
};

// Journey color options for selection
export const journeyColorOptions = [
  { id: 'coral' as const, value: '#e54b50', name: 'Coral', textColor: '#FFFFFF' },
  { id: 'teal' as const, value: '#30CFC5', name: 'Teal', textColor: '#FFFFFF' },
  { id: 'purple' as const, value: '#8579ED', name: 'Purple', textColor: '#FFFFFF' },
  { id: 'amber' as const, value: '#F59E0B', name: 'Amber', textColor: '#000000' },
  { id: 'oceanBlue' as const, value: '#2E9FE3', name: 'Ocean Blue', textColor: '#FFFFFF' },
  { id: 'magenta' as const, value: '#E9608A', name: 'Magenta', textColor: '#FFFFFF' },
  { id: 'tangerine' as const, value: '#E85D0F', name: 'Tangerine', textColor: '#000000' },
  { id: 'evergreen' as const, value: '#1E8449', name: 'Evergreen', textColor: '#FFFFFF' },
  { id: 'terraCotta' as const, value: '#E27D60', name: 'Terra Cotta', textColor: '#FFFFFF' },
  { id: 'steelBlue' as const, value: '#4682B4', name: 'Steel Blue', textColor: '#FFFFFF' },
];
