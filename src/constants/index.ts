// Storage keys with project ID for uniqueness
export const STORAGE_KEYS = {
  THEME: 'app_theme_mode_9afaf736',
  USER_PREFERENCES: 'user_preferences_9afaf736',
  JOURNEY_DATA: 'journey_data_9afaf736',
  JOURNEY_STORE: 'journey_store_9afaf736',
  WAYPOINT_STORE: 'waypoint_store_9afaf736',
  AUTH_TOKEN: 'auth_token_9afaf736',
  AUTH_STORE: 'auth_store_9afaf736',
  APP_STORE: 'app_store_9afaf736',
  FIRST_LAUNCH: 'first_launch_9afaf736',
  LAST_ACTIVE_JOURNEY: 'last_active_journey_9afaf736',
};

// Re-export theme system
export { lightTheme, darkTheme } from './themes';
export { COLORS, JOURNEY_COLORS } from './colors';

// Journey color options for UI selection
export const journeyColorOptions = [
  { id: 'coral', name: 'Coral', value: '#e54b50', textColor: '#FFFFFF' },
  { id: 'teal', name: 'Teal', value: '#30CFC5', textColor: '#FFFFFF' },
  { id: 'purple', name: 'Purple', value: '#8579ED', textColor: '#FFFFFF' },
  { id: 'amber', name: 'Amber', value: '#F59E0B', textColor: '#000000' },
  { id: 'oceanBlue', name: 'Ocean Blue', value: '#2E9FE3', textColor: '#FFFFFF' },
  { id: 'magenta', name: 'Magenta', value: '#E9608A', textColor: '#FFFFFF' },
  { id: 'tangerine', name: 'Tangerine', value: '#E85D0F', textColor: '#000000' },
  { id: 'evergreen', name: 'Evergreen', value: '#1E8449', textColor: '#FFFFFF' },
  { id: 'terraCotta', name: 'Terra Cotta', value: '#E27D60', textColor: '#FFFFFF' },
  { id: 'steelBlue', name: 'Steel Blue', value: '#4682B4', textColor: '#FFFFFF' },
] as const;

// App constants
export const APP_CONFIG = {
  PROJECT_ID: '9afaf736-bdc4-4ad5-bbc8-0dc37ac008ed',
  VERSION: '1.0.0',
  BUILD: '1',
};

// Theme-related exports
export type { Theme, ThemeMode, ThemeContextType } from '../types/theme';
export { useTheme, useThemeMode, useThemeColors, useIsDarkMode, useIsLightMode } from '../contexts/ThemeContext';
export { themeUtils } from '../utils/themeUtils';
export { useThemeStyles } from '../hooks/useThemeStyles';
