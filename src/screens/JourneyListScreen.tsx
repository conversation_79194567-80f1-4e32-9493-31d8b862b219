
import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable
} from 'react-native';
import { Search, Filter, Settings, Flag, Star, Plus } from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { useWaypointStore } from '../stores/waypointStore';
import { useTheme } from '../contexts/ThemeContext';
import { formatTimeAgo } from '../utils/dateUtils';

interface JourneyListScreenProps {
  navigation: any;
}

export default function JourneyListScreen({ navigation }: JourneyListScreenProps) {
  const { theme } = useTheme();
  const journeys = useJourneyStore(state => state.journeys);
  const waypoints = useWaypointStore(state => state.waypoints);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 60,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
      backgroundColor: theme.colors.background.primary
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text.primary
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8
    },
    actionButton: {
      padding: 8,
      borderRadius: 8
    },
    scrollView: {
      flex: 1
    },
    journeyList: {
      padding: 16,
      paddingBottom: 100
    },
    journeyCard: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 12,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2
    },
    cardContent: {
      padding: 16
    },
    journeyInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12
    },
    colorCircle: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: 8
    },
    journeyTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text.primary,
      flex: 1
    },
    metadataRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    metricsGroup: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    metricItem: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    metadataText: {
      fontSize: 12,
      color: theme.colors.text.secondary
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 8,
      textAlign: 'center'
    },
    emptySubtitle: {
      fontSize: 14,
      color: theme.colors.text.secondary,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 24
    },
    emptyButton: {
      backgroundColor: theme.primary.default,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      gap: 8
    },
    emptyButtonText: {
      color: theme.primary.contrast,
      fontSize: 16,
      fontWeight: '600'
    },
    fab: {
      position: 'absolute',
      bottom: 24,
      right: 24,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.primary.default,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8
    }
  }), [theme]);



  const renderJourneyCard = (journey: any) => {
    // Calculate entry and milestone counts for this journey
    const journeyWaypoints = waypoints.filter(waypoint => waypoint.journeyId === journey.id);
    const entryCount = journeyWaypoints.length;
    const milestoneCount = journeyWaypoints.filter(waypoint => waypoint.isMilestone).length;

    return (
      <Pressable
        key={journey.id}
        style={styles.journeyCard}
        onPress={() => navigation.navigate('JourneyDetail', { journeyId: journey.id })}
      >
        <View style={styles.cardContent}>
          <View style={styles.journeyInfo}>
            <View style={[styles.colorCircle, { backgroundColor: journey.color }]} />
            <Text style={styles.journeyTitle}>{journey.title}</Text>
          </View>
          
          <View style={styles.metadataRow}>
            <View style={styles.metricsGroup}>
              <View style={[styles.metricItem, { marginRight: 12 }]}>
                <Flag size={14} color={theme.colors.text.muted} style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{entryCount}</Text>
              </View>
              <View style={styles.metricItem}>
                <Star size={14} color={theme.colors.text.muted} style={{ marginRight: 4 }} />
                <Text style={styles.metadataText}>{milestoneCount}</Text>
              </View>
            </View>
            <Text style={styles.metadataText}>{formatTimeAgo(journey.updatedAt)}</Text>
          </View>
        </View>
      </Pressable>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>waymarker.</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => {
              // Handle search
            }}
          >
            <Search size={20} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => {
              // Handle filter
            }}
          >
            <Filter size={20} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('Settings')}
          >
            <Settings size={20} color={theme.colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {journeys.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>Start Your Journey</Text>
          <Text style={styles.emptySubtitle}>
            Create your first journey and begin tracking meaningful moments and milestones.
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={() => navigation.navigate('CreateJourney')}
          >
            <Plus size={20} color={theme.primary.contrast} />
            <Text style={styles.emptyButtonText}>Create Journey</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.journeyList}
          showsVerticalScrollIndicator={false}
        >
          {journeys.map(renderJourneyCard)}
        </ScrollView>
      )}

      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('CreateJourney')}
      >
        <Plus size={24} color={theme.primary.contrast} />
      </TouchableOpacity>
    </View>
  );
}

