import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../contexts/ThemeContext';
import { useThemeStyles } from '../hooks/useThemeStyles';
import ThemeShowcase from '../components/examples/ThemeShowcase';
import { Palette } from 'lucide-react-native';

export function ThemeDemo() {
  const { theme } = useTheme();
  const { styles: themeStyles } = useThemeStyles();

  const styles = StyleSheet.create({
    header: {
      backgroundColor: theme.colors.background.secondary,
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text.primary,
    },
    headerSubtitle: {
      fontSize: 14,
      color: theme.colors.text.secondary,
      marginTop: 2,
    },
  });

  return (
    <SafeAreaView style={themeStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={{
            backgroundColor: theme.primary.default,
            padding: 8,
            borderRadius: 8,
          }}>
            <Palette size={20} color={theme.primary.contrast} />
          </View>
          <View>
            <Text style={styles.headerTitle}>Theme System Demo</Text>
            <Text style={styles.headerSubtitle}>Comprehensive theme showcase</Text>
          </View>
        </View>
      </View>

      {/* Content */}
      <ThemeShowcase />
    </SafeAreaView>
  );
}

export default ThemeDemo;