import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable
} from 'react-native';
import { X } from 'lucide-react-native';
import { useWaypointStore } from '../stores/waypointStore';
import { useJourneyStore } from '../stores/journeyStore';
import { useTheme } from '../contexts/ThemeContext';
import WaypointForm from '../components/forms/WaypointForm';

interface AddWaypointScreenProps {
  navigation: any;
  route: {
    params: {
      journeyId: string;
    };
  };
}

export default function AddWaypointScreen({ navigation, route }: AddWaypointScreenProps) {
  const { theme } = useTheme();
  const { journeyId } = route.params;
  
  const createWaypoint = useWaypointStore(state => state.createWaypoint);
  const updateJourney = useJourneyStore(state => state.updateJourney);
  const journey = useJourneyStore(state => state.getJourneyById(journeyId));
  
  // Get journey color from theme
  const journeyColor = journey ? theme.journey[journey.color as keyof typeof theme.journey] || theme.journey.coral : theme.journey.coral;

  const handleSubmit = (data: {
    content: string;
    date: Date;
    sentiment: number;
    impact: 'low' | 'medium' | 'high';
    isMilestone: boolean;
  }) => {
    if (!journey) return;

    createWaypoint({
      journeyId,
      ...data
    });

    // Update journey counts
    updateJourney(journeyId, {
      waypointCount: journey.waypointCount + 1,
      milestoneCount: journey.milestoneCount + (data.isMilestone ? 1 : 0),
      updatedAt: new Date()
    });

    navigation.goBack();
  };



  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 60,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
      backgroundColor: theme.colors.background.primary
    },
    closeButton: {
      padding: 8
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary
    },
    placeholder: {
      width: 40
    }
  }), [theme]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable 
          style={styles.closeButton}
          onPress={() => navigation.goBack()}
        >
          <X size={24} color={theme.colors.text.primary} />
        </Pressable>
        <Text style={styles.headerTitle}>New Waypoint</Text>
        <View style={styles.placeholder} />
      </View>

      <WaypointForm
        onSubmit={handleSubmit}
        buttonText="Add Waypoint"
        journeyColor={journeyColor.default}
      />
    </View>
  );
}
