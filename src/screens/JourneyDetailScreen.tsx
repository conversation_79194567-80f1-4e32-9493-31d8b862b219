
import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable,
  Modal
} from 'react-native';
import {
  ArrowLeft,
  Search,
  Filter,
  MoreHorizontal,
  MoreVertical,
  Flag,
  Star,
  Zap,
  Plus,
  TrendingUp,
  Play,
  SkipBack,
  SkipForward,
  ChevronLeft,
  ChevronRight,
  Edit,
  Trash2,
  X
} from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { useWaypointStore } from '../stores/waypointStore';
import { useTheme } from '../contexts/ThemeContext';
import ConfirmationModal from '../components/modals/ConfirmationModal';
import WaypointForm from '../components/forms/WaypointForm';
import DropdownMenu, { DropdownOption } from '../components/ActionSheet';
import { formatDate } from '../utils/dateUtils';
import * as Haptics from 'expo-haptics';

interface JourneyDetailScreenProps {
  navigation: any;
  route: {
    params: {
      journeyId: string;
    };
  };
}

export default function JourneyDetailScreen({ navigation, route }: JourneyDetailScreenProps) {
  const { theme } = useTheme();
  const { journeyId } = route.params;
  const [activeTab, setActiveTab] = useState<'timeline' | 'path' | 'insights'>('timeline');
  const [showJourneyMenu, setShowJourneyMenu] = useState(false);
  const [showWaypointMenu, setShowWaypointMenu] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [showDeleteJourneyModal, setShowDeleteJourneyModal] = useState(false);
  const [showDeleteWaypointModal, setShowDeleteWaypointModal] = useState<string | null>(null);
  const [editingWaypoint, setEditingWaypoint] = useState<string | null>(null);
  
  const journey = useJourneyStore(state => state.getJourneyById(journeyId));
  const deleteJourney = useJourneyStore(state => state.deleteJourney);
  const updateJourney = useJourneyStore(state => state.updateJourney);
  const getWaypointsByJourney = useWaypointStore(state => state.getWaypointsByJourney);
  const updateWaypoint = useWaypointStore(state => state.updateWaypoint);
  const deleteWaypoint = useWaypointStore(state => state.deleteWaypoint);
  const waypoints = getWaypointsByJourney(journeyId);

  if (!journey) {
    navigation.goBack();
    return null;
  }

  // Map hex color to theme journey color key
  const getJourneyColorKey = (hexColor: string): keyof typeof theme.journey => {
    const colorMap: Record<string, keyof typeof theme.journey> = {
      '#2196F3': 'oceanBlue',    // Career Transition
      '#10B981': 'evergreen',    // Fitness Journey  
      '#8B5CF6': 'purple',       // Learning Spanish
      '#F97316': 'tangerine',    // Home Renovation
      '#EF4444': 'coral',        // Red
      '#14B8A6': 'teal',         // Teal
      '#EC4899': 'magenta',      // Pink
      '#6366F1': 'purple',       // Indigo
      '#F59E0B': 'amber',        // Amber
      '#6B7280': 'steelBlue'     // Gray
    };
    return colorMap[hexColor] || 'coral';
  };
  
  const journeyColorKey = getJourneyColorKey(journey.color);
  const journeyColor = theme.journey[journeyColorKey];

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    header: {
      paddingHorizontal: 16,
      paddingTop: 60,
      paddingBottom: 16,
      backgroundColor: theme.colors.background.primary
    },
    headerTop: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1
    },
    backButton: {
      padding: 8,
      marginRight: 8
    },
    journeyInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1
    },
    colorCircle: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: journeyColor.default,
      marginRight: 12
    },
    journeyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text.primary,
      flex: 1
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8
    },
    actionButton: {
      padding: 8
    },
    headerStats: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginLeft: 44
    },
    headerStatsLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8
    },
    statText: {
      fontSize: 14,
      color: theme.colors.text.secondary
    },
    tabContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.background.primary,
      paddingHorizontal: 16,
      marginBottom: 8
    },
    tab: {
      paddingVertical: 12,
      paddingHorizontal: 16,
      marginRight: 24,
      alignItems: 'center',
      flexDirection: 'row',
      gap: 6
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: journeyColor.default
    },
    tabText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text.secondary
    },
    activeTabText: {
      color: journeyColor.default,
      fontWeight: '600'
    },
    tabContent: {
      flex: 1,
      padding: 16
    },
    waypointCard: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2
    },
    milestoneCard: {
      backgroundColor: journeyColor.muted
    },
    waypointHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8
    },
    waypointHeaderLeft: {
      flex: 1
    },
    waypointDate: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.text.muted,
      marginBottom: 4
    },

    waypointMenuButton: {
      padding: 4
    },
    waypointContent: {
      fontSize: 16,
      lineHeight: 24,
      color: theme.colors.text.primary,
      marginBottom: 12
    },
    waypointMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    sentimentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 2
    },
    impactContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8
    },
    impactLabel: {
      fontSize: 12,
      color: theme.colors.text.muted
    },
    impactBarContainer: {
      width: 60,
      height: 4,
      backgroundColor: theme.colors.border.secondary,
      borderRadius: 2,
      overflow: 'hidden'
    },
    impactBar: {
      height: '100%',
      backgroundColor: journeyColor.default,
      borderRadius: 2
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border.secondary,
      marginVertical: 12
    },
    fab: {
      position: 'absolute',
      bottom: 24,
      right: 24,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: journeyColor.default,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8
    }
  }), [theme, journeyColor]);

  const handleEditJourney = () => {
    navigation.navigate('CreateJourney', { journeyId });
  };

  const handleDeleteJourney = () => {
    setShowDeleteJourneyModal(true);
  };

  const confirmDeleteJourney = () => {
    deleteJourney(journeyId);
    setShowDeleteJourneyModal(false);
    navigation.goBack();
  };

  const handleEditWaypoint = (waypointId: string) => {
    setEditingWaypoint(waypointId);
  };

  const handleDeleteWaypoint = (waypointId: string) => {
    setShowDeleteWaypointModal(waypointId);
  };

  const confirmDeleteWaypoint = (waypointId: string) => {
    const waypoint = waypoints.find(w => w.id === waypointId);
    if (waypoint) {
      deleteWaypoint(waypointId);
      // Update journey counts
      updateJourney(journeyId, {
        waypointCount: journey.waypointCount - 1,
        milestoneCount: journey.milestoneCount - (waypoint.isMilestone ? 1 : 0),
        updatedAt: new Date()
      });
    }
    setShowDeleteWaypointModal(null);
  };

  const handleUpdateWaypoint = (data: {
    content: string;
    date: Date;
    sentiment: number;
    impact: 'low' | 'medium' | 'high';
    isMilestone: boolean;
  }) => {
    if (editingWaypoint) {
      const oldWaypoint = waypoints.find(w => w.id === editingWaypoint);
      updateWaypoint(editingWaypoint, data);
      
      // Update milestone count if changed
      if (oldWaypoint && oldWaypoint.isMilestone !== data.isMilestone) {
        updateJourney(journeyId, {
          milestoneCount: journey.milestoneCount + (data.isMilestone ? 1 : -1),
          updatedAt: new Date()
        });
      }
    }
    setEditingWaypoint(null);
  };

  // Journey Menu Options
  const journeyOptions: DropdownOption[] = [
    {
      label: 'Edit Journey',
      icon: <Edit size={20} color={theme.colors.text.primary} />,
      onPress: handleEditJourney
    },
    {
      label: 'Delete Journey',
      icon: <Trash2 size={20} color={theme.status.error} />,
      onPress: handleDeleteJourney,
      destructive: true
    }
  ];

  // Waypoint Menu Options
  const getWaypointOptions = (waypointId: string): DropdownOption[] => [
    {
      label: 'Edit Waypoint',
      icon: <Edit size={20} color={theme.colors.text.primary} />,
      onPress: () => handleEditWaypoint(waypointId)
    },
    {
      label: 'Delete Waypoint',
      icon: <Trash2 size={20} color={theme.status.error} />,
      onPress: () => handleDeleteWaypoint(waypointId),
      destructive: true
    }
  ];

  // Handle menu button press with position tracking
  const handleJourneyMenuPress = (event: any) => {
    const { pageX, pageY } = event.nativeEvent;
    setMenuPosition({ x: pageX, y: pageY });
    setShowJourneyMenu(true);
  };

  const handleWaypointMenuPress = (waypointId: string, event: any) => {
    const { pageX, pageY } = event.nativeEvent;
    setMenuPosition({ x: pageX, y: pageY });
    setShowWaypointMenu(waypointId);
  };

  const getSentimentBolts = (sentiment: number) => {
    const filledCount = Math.ceil((Math.abs(sentiment) / 100) * 3);
    return Array(3).fill(0).map((_, i) => {
      const isFilled = i < filledCount;
      return (
        <Zap
          key={i}
          size={12}
          color={isFilled ? journeyColor.default : theme.colors.text.muted}
          fill={isFilled ? journeyColor.default : 'none'}
        />
      );
    });
  };

  const getImpactBar = (impact: string) => {
    const width = impact === 'low' ? '33%' : impact === 'medium' ? '66%' : '100%';
    return (
      <View style={styles.impactBarContainer}>
        <View style={[styles.impactBar, { width }]} />
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'timeline':
        return (
          <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
            {waypoints.map((waypoint) => (
              <View
                key={waypoint.id}
                style={[
                  styles.waypointCard,
                  waypoint.isMilestone && styles.milestoneCard
                ]}
              >
                <View style={styles.waypointHeader}>
                  <View style={styles.waypointHeaderLeft}>
                    <Text style={styles.waypointDate}>
                      {formatDate(waypoint.date).toUpperCase()}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.waypointMenuButton}
                    onPress={(event) => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      handleWaypointMenuPress(waypoint.id, event);
                    }}
                  >
                    <MoreVertical size={16} color={theme.colors.text.secondary} />
                  </TouchableOpacity>
                </View>
                
                <Text style={styles.waypointContent}>
                  {waypoint.content}
                </Text>
                
                <View style={styles.separator} />
                
                <View style={styles.waypointMeta}>
                  <View style={styles.sentimentContainer}>
                    {getSentimentBolts(waypoint.sentiment)}
                  </View>
                  
                  <View style={styles.impactContainer}>
                    <Text style={styles.impactLabel}>–</Text>
                    {getImpactBar(waypoint.impact)}
                    <Text style={styles.impactLabel}>+</Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
        );
        
      case 'path':
        return (
          <View style={styles.tabContent}>
            <Text style={[styles.waypointContent, { textAlign: 'center', color: theme.colors.text.secondary }]}>
              Path visualization coming soon...
            </Text>
          </View>
        );
        
      case 'insights':
        return (
          <View style={styles.tabContent}>
            <Text style={[styles.waypointContent, { textAlign: 'center', color: theme.colors.text.secondary }]}>
              Journey insights coming soon...
            </Text>
          </View>
        );
        
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={styles.headerLeft}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <ArrowLeft size={24} color={theme.colors.text.primary} />
            </TouchableOpacity>
            <View style={styles.journeyInfo}>
              <View style={styles.colorCircle} />
              <Text style={styles.journeyTitle} numberOfLines={2}>
                {journey.title}
              </Text>
            </View>
          </View>
          

        </View>
        
        <View style={styles.headerStats}>
          <View style={styles.headerStatsLeft}>
            <Flag size={16} color={theme.colors.text.secondary} />
            <Text style={styles.statText}>{waypoints.length}</Text>
            <Text style={styles.statText}>
              Since {formatDate(journey.startDate)}
            </Text>
          </View>
          
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Search size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Filter size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleJourneyMenuPress}
            >
              <MoreVertical size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'timeline' && styles.activeTab]}
          onPress={() => setActiveTab('timeline')}
        >
          <TrendingUp size={16} color={activeTab === 'timeline' ? journeyColor.default : theme.colors.text.secondary} />
          <Text style={[styles.tabText, activeTab === 'timeline' && styles.activeTabText]}>
            Timeline
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'path' && styles.activeTab]}
          onPress={() => setActiveTab('path')}
        >
          <Play size={16} color={activeTab === 'path' ? journeyColor.default : theme.colors.text.secondary} />
          <Text style={[styles.tabText, activeTab === 'path' && styles.activeTabText]}>
            Path
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'insights' && styles.activeTab]}
          onPress={() => setActiveTab('insights')}
        >
          <TrendingUp size={16} color={activeTab === 'insights' ? journeyColor.default : theme.colors.text.secondary} />
          <Text style={[styles.tabText, activeTab === 'insights' && styles.activeTabText]}>
            Insights
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {renderTabContent()}

      {/* FAB */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('AddWaypoint', { journeyId })}
      >
        <Plus size={24} color={journeyColor.contrast} />
      </TouchableOpacity>

      {/* Journey Menu */}
      <DropdownMenu
        visible={showJourneyMenu}
        onClose={() => setShowJourneyMenu(false)}
        options={journeyOptions}
        position={menuPosition}
      />

      {/* Waypoint Menu */}
      {showWaypointMenu && (
        <DropdownMenu
          visible={!!showWaypointMenu}
          onClose={() => setShowWaypointMenu(null)}
          options={getWaypointOptions(showWaypointMenu)}
          position={menuPosition}
        />
      )}

      {/* Delete Journey Modal */}
      <ConfirmationModal
        visible={showDeleteJourneyModal}
        title="Delete Journey"
        message={`Are you sure you want to delete "${journey.title}"? This action cannot be undone and will also delete all waypoints.`}
        confirmText="Delete"
        onConfirm={confirmDeleteJourney}
        onCancel={() => setShowDeleteJourneyModal(false)}
        destructive
      />

      {/* Delete Waypoint Modal */}
      {showDeleteWaypointModal && (
        <ConfirmationModal
          visible={!!showDeleteWaypointModal}
          title="Delete Waypoint"
          message="Are you sure you want to delete this waypoint? This action cannot be undone."
          confirmText="Delete"
          onConfirm={() => confirmDeleteWaypoint(showDeleteWaypointModal)}
          onCancel={() => setShowDeleteWaypointModal(null)}
          destructive
        />
      )}

      {/* Edit Waypoint Modal */}
      <Modal
        visible={!!editingWaypoint}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
          <View style={styles.header}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => setEditingWaypoint(null)}
            >
              <X size={24} color={theme.colors.text.primary} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Edit Waypoint</Text>
            <View style={{ width: 40 }} />
          </View>
          
          {editingWaypoint && (
            <WaypointForm
              initialData={waypoints.find(w => w.id === editingWaypoint)}
              onSubmit={handleUpdateWaypoint}
              buttonText="Save Changes"
              journeyColor={journeyColor.default}
            />
          )}
        </View>
      </Modal>
    </View>
  );
}