
import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable
} from 'react-native';
import {
  ArrowLeft,
  ChevronRight,
  ExternalLink,
  User,
  Palette,
  Bell,
  HelpCircle,
  Shield,
  FileText,
  Database,
  Info,
  Globe
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import ThemeSelector from '../components/ui/ThemeSelector';
import { APP_CONFIG } from '../constants';

interface SettingsScreenProps {
  navigation: any;
}

interface SettingsScreenProps {
  navigation: any;
}

export default function SettingsScreen({ navigation }: SettingsScreenProps) {
  const { theme, isDarkMode } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 60,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
      backgroundColor: theme.colors.background.primary
    },
    backButton: {
      padding: 8
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary
    },
    placeholder: {
      width: 40
    },
    content: {
      flex: 1,
      padding: 16
    },
    sectionHeader: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginTop: 24,
      marginBottom: 12,
      marginHorizontal: 4
    },
    section: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 12,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    accountDescription: {
      fontSize: 14,
      color: theme.colors.text.secondary,
      lineHeight: 20,
      padding: 16,
      paddingBottom: 0
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary
    },
    settingIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.background.tertiary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12
    },
    settingContent: {
      flex: 1
    },
    settingTitle: {
      fontSize: 16,
      color: theme.colors.text.primary,
      marginBottom: 2
    },
    settingSubtitle: {
      fontSize: 12,
      color: theme.colors.text.secondary
    },
  }), [theme, isDarkMode]);

  const renderSectionHeader = (title: string) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const renderSettingItem = (
    icon: any,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIcon}>
        {React.createElement(icon, { size: 20, color: theme.colors.text.secondary })}
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightElement || (onPress && (
        <ChevronRight size={16} color={theme.colors.text.muted} />
      ))}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color={theme.colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSectionHeader('Account')}
        <View style={styles.section}>
          <Text style={styles.accountDescription}>
            Sign in to sync your journeys across devices and backup your data
          </Text>
          
          {renderSettingItem(
            User,
            'Continue with Google',
            undefined,
            () => {
              // Handle Google sign in
            }
          )}
          
          {renderSettingItem(
            User,
            'Continue with Email',
            undefined,
            () => {
              // Handle email sign in
            }
          )}
        </View>

        {renderSectionHeader('Appearance')}
        <View style={styles.section}>
          {renderSettingItem(
            Palette,
            'Theme',
            'Choose your preferred theme',
            undefined,
            <ThemeSelector variant="horizontal" showLabels={false} size="small" />
          )}
          
          {renderSettingItem(
            Palette,
            'Theme Demo',
            'View theme system showcase',
            () => navigation.navigate('ThemeDemo')
          )}
        </View>

        {renderSectionHeader('Notifications')}
        <View style={styles.section}>
          {renderSettingItem(
            Bell,
            'Notification Settings',
            undefined,
            () => {
              // Handle notification settings
            }
          )}
        </View>

        {renderSectionHeader('Support')}
        <View style={styles.section}>
          {renderSettingItem(
            HelpCircle,
            'Help & FAQ',
            undefined,
            () => {
              // Handle help
            }
          )}
          
          {renderSettingItem(
            Shield,
            'Privacy Policy',
            undefined,
            () => {
              // Handle privacy policy
            }
          )}
          
          {renderSettingItem(
            FileText,
            'Terms of Service',
            undefined,
            () => {
              // Handle terms
            }
          )}
        </View>

        {renderSectionHeader('Data & Storage')}
        <View style={styles.section}>
          {renderSettingItem(
            Database,
            'Data Management',
            undefined,
            () => {
              // Handle data management
            }
          )}
        </View>

        {renderSectionHeader('About')}
        <View style={styles.section}>
          {renderSettingItem(
            Info,
            'Version',
            `v${APP_CONFIG.VERSION} (${APP_CONFIG.BUILD})`,
            undefined
          )}
          
          {renderSettingItem(
            Globe,
            'waymarkerapp.com',
            undefined,
            () => {
              // Handle website link
            },
            <ExternalLink size={16} color={theme.colors.text.muted} />
          )}
        </View>
      </ScrollView>
    </View>
  );
}


