
import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Pressable,
  Alert
} from 'react-native';
import { X, Check, Calendar } from 'lucide-react-native';
import { useJourneyStore } from '../stores/journeyStore';
import { useTheme } from '../contexts/ThemeContext';
import { formatDisplayDate } from '../utils/dateUtils';

interface CreateJourneyScreenProps {
  navigation: any;
}

export default function CreateJourneyScreen({ navigation, route }: CreateJourneyScreenProps) {
  const { theme } = useTheme();
  const journeyId = route.params?.journeyId;
  const isEditing = !!journeyId;
  
  const getJourneyById = useJourneyStore(state => state.getJourneyById);
  const existingJourney = isEditing ? getJourneyById(journeyId) : null;
  
  const [title, setTitle] = useState(existingJourney?.title || '');
  const [description, setDescription] = useState(existingJourney?.description || '');
  const [selectedColor, setSelectedColor] = useState(existingJourney?.color || 'coral');
  const [status, setStatus] = useState<'active' | 'done'>(existingJourney?.status || 'active');
  const [startDate] = useState(existingJourney?.startDate || new Date());
  
  const createJourney = useJourneyStore(state => state.createJourney);
  const updateJourney = useJourneyStore(state => state.updateJourney);
  
  const isValid = title.trim().length > 0;
  
  // Get journey colors from theme
  const journeyColorKeys = Object.keys(theme.journey) as (keyof typeof theme.journey)[];
  const selectedJourneyColor = theme.journey[selectedColor as keyof typeof theme.journey] || theme.journey.coral;

  const handleSubmit = () => {
    if (!isValid) return;

    if (isEditing && journeyId) {
      updateJourney(journeyId, {
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        status
      });
    } else {
      createJourney({
        title: title.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
        startDate,
        status: 'active'
      });
    }

    navigation.goBack();
  };



  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: 60,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border.secondary,
      backgroundColor: theme.colors.background.primary
    },
    closeButton: {
      padding: 8
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary
    },
    placeholder: {
      width: 40
    },
    content: {
      flex: 1,
      padding: 16
    },
    section: {
      marginBottom: 24
    },
    label: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 8
    },
    required: {
      color: theme.status.error
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      color: theme.colors.text.primary,
      backgroundColor: theme.colors.surface.input
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top'
    },
    dateInput: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      borderRadius: 12,
      padding: 16,
      backgroundColor: theme.colors.surface.input
    },
    dateText: {
      fontSize: 16,
      color: theme.colors.text.primary
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12
    },
    colorOption: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: 'transparent'
    },
    selectedColor: {
      borderColor: theme.colors.text.primary,
      transform: [{ scale: 1.1 }]
    },
    statusContainer: {
      flexDirection: 'row',
      gap: 12
    },
    statusOption: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      alignItems: 'center',
      backgroundColor: theme.colors.surface.input
    },
    selectedStatus: {
      backgroundColor: selectedJourneyColor.light,
      borderColor: selectedJourneyColor.default
    },
    statusText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text.secondary
    },
    selectedStatusText: {
      color: selectedJourneyColor.dark,
      fontWeight: '600'
    },
    footer: {
      padding: 16,
      paddingBottom: 32,
      backgroundColor: theme.colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border.secondary
    },
    createButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      paddingVertical: 16,
      borderRadius: 12,
      backgroundColor: selectedJourneyColor.default
    },
    disabledButton: {
      backgroundColor: theme.colors.text.muted,
      opacity: 0.6
    },
    createButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: selectedJourneyColor.contrast
    }
  }), [theme, selectedJourneyColor]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable 
          style={styles.closeButton}
          onPress={() => navigation.goBack()}
        >
          <X size={24} color={theme.colors.text.primary} />
        </Pressable>
        <Text style={styles.headerTitle}>{isEditing ? 'Edit Journey' : 'Create Journey'}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.label}>
            Title <Text style={styles.required}>*</Text>
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter journey title"
            value={title}
            onChangeText={setTitle}
            placeholderTextColor={theme.colors.text.muted}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Describe your journey..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            placeholderTextColor={theme.colors.text.muted}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Start Date</Text>
          <View style={styles.dateInput}>
            <Text style={styles.dateText}>{formatDisplayDate(startDate)}</Text>
            <Calendar size={20} color={theme.colors.text.muted} />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Journey Color</Text>
          <View style={styles.colorGrid}>
            {journeyColorKeys.map((colorKey) => {
              const journeyColor = theme.journey[colorKey];
              return (
                <TouchableOpacity
                  key={colorKey}
                  style={[
                    styles.colorOption,
                    { backgroundColor: journeyColor.default },
                    selectedColor === colorKey && styles.selectedColor
                  ]}
                  onPress={() => setSelectedColor(colorKey)}
                >
                  {selectedColor === colorKey && (
                    <Check size={20} color={journeyColor.contrast} />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {isEditing && (
          <View style={styles.section}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.statusContainer}>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'active' && styles.selectedStatus
                ]}
                onPress={() => setStatus('active')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'active' && styles.selectedStatusText
                ]}>Active</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  status === 'done' && styles.selectedStatus
                ]}
                onPress={() => setStatus('done')}
              >
                <Text style={[
                  styles.statusText,
                  status === 'done' && styles.selectedStatusText
                ]}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.createButton,
            !isValid && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!isValid}
        >
          <Check size={20} color="#FFFFFF" />
          <Text style={styles.createButtonText}>{isEditing ? 'Save Changes' : 'Create Journey'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}


