// Theme System Types
export type ThemeMode = 'light' | 'dark' | 'system';

// Base color structure for both light and dark themes
export interface BaseColors {
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    elevated: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
    inverse: string;
  };
  border: {
    primary: string;
    secondary: string;
    focus: string;
  };
  surface: {
    card: string;
    overlay: string;
    input: string;
  };
}

// Status colors (same for both themes)
export interface StatusColors {
  error: string;
  warning: string;
  success: string;
  info: string;
}

// Primary app color variants
export interface PrimaryColors {
  default: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string;
}

// Journey color structure
export interface JourneyColorVariant {
  default: string;
  light: string;
  dark: string;
  muted: string;
  contrast: string;
}

export interface JourneyColors {
  coral: JourneyColorVariant;
  teal: JourneyColorVariant;
  purple: JourneyColorVariant;
  amber: JourneyColorVariant;
  oceanBlue: JourneyColorVariant;
  magenta: JourneyColorVariant;
  tangerine: JourneyColorVariant;
  evergreen: JourneyColorVariant;
  terraCotta: JourneyColorVariant;
  steelBlue: JourneyColorVariant;
}

// Journey color option for selection
export interface JourneyColorOption {
  id: keyof JourneyColors;
  value: string;
  name: string;
  textColor: string;
}

// Focus state system
export interface FocusRing {
  color: string;
  colorDark: string;
  width: string;
  offset: string;
  style: 'solid' | 'dashed' | 'dotted';
}

export interface FocusAlternatives {
  coral: string;
  white: string;
  dark: string;
}

export interface FocusContrast {
  minimum: number;
  preferred: number;
}

export interface FocusState {
  ring: FocusRing;
  alternatives: FocusAlternatives;
  contrast: FocusContrast;
}

// Opacity system
export interface OpacityLevels {
  subtle: number;  // 0.1
  light: number;   // 0.3
  medium: number;  // 0.5
  high: number;    // 0.8
}

export interface OpacityFunctions {
  hover: (color: string) => string;
  pressed: (color: string) => string;
  disabled: (color: string) => string;
  overlay: (color: string) => string;
}

export interface OpacitySystem extends OpacityLevels {
  functions: OpacityFunctions;
}

// Complete theme structure
export interface Theme {
  mode: ThemeMode;
  colors: BaseColors;
  status: StatusColors;
  primary: PrimaryColors;
  journey: JourneyColors;
  focus: FocusState;
  opacity: OpacitySystem;
}

// Theme context type
export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  isLightMode: boolean;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
  
  // Utility functions
  getFocusStyle: (variant?: keyof FocusAlternatives) => object;
  getFocusVisibleStyle: (variant?: keyof FocusAlternatives) => object;
  getAccessibleTextColor: (backgroundColor: string) => string;
  getJourneyColorVariant: (colorId: keyof JourneyColors, variant: keyof JourneyColorVariant) => string;
  withOpacity: (color: string, opacityLevel: keyof OpacityLevels) => string;
}

// Accessibility matrix entry
export interface AccessibilityEntry {
  ratio: number;
  status: 'AA_PASS' | 'AA_LARGE' | 'FOCUS_PASS' | 'FAIL';
  textColor: string;
  context?: string;
}

export interface AccessibilityMatrix {
  [key: string]: AccessibilityEntry;
}
