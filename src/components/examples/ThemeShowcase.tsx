import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { JourneyColors } from '../../types/theme';
import { Heart, Star, Settings, User } from 'lucide-react-native';
import ThemeSelector from '../ui/ThemeSelector';

export function ThemeShowcase() {
  const { theme, getJourneyColorVariant, withOpacity, getFocusStyle } = useTheme();
  const [inputValue, setInputValue] = useState('');
  const [selectedJourneyColor, setSelectedJourneyColor] = useState<keyof JourneyColors>('coral');

  const journeyColorOptions: { id: keyof JourneyColors; name: string }[] = [
    { id: 'coral', name: 'Coral' },
    { id: 'teal', name: '<PERSON><PERSON>' },
    { id: 'purple', name: '<PERSON>' },
    { id: 'amber', name: '<PERSON>' },
    { id: 'oceanBlue', name: '<PERSON> Blue' },
    { id: 'magenta', name: 'Ma<PERSON><PERSON>' },
    { id: 'tangerine', name: '<PERSON><PERSON><PERSON>' },
    { id: 'evergreen', name: 'Evergreen' },
    { id: 'terraCotta', name: 'Terra Cotta' },
    { id: 'steelBlue', name: 'Steel Blue' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary,
    },
    scrollContent: {
      padding: 20,
      gap: 24,
    },
    section: {
      gap: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 8,
    },
    card: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    colorSwatch: {
      width: 60,
      height: 60,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'transparent',
    },
    selectedColorSwatch: {
      borderColor: theme.colors.border.focus,
    },
    colorLabel: {
      fontSize: 10,
      fontWeight: '500',
      textAlign: 'center',
      marginTop: 2,
    },
    buttonRow: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
    },
    primaryButton: {
      backgroundColor: theme.primary.default,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8,
      flex: 1,
      minWidth: 120,
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.border.primary,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8,
      flex: 1,
      minWidth: 120,
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '500',
    },
    primaryButtonText: {
      color: theme.primary.contrast,
    },
    secondaryButtonText: {
      color: theme.colors.text.primary,
    },
    input: {
      backgroundColor: theme.colors.surface.input,
      borderWidth: 1,
      borderColor: theme.colors.border.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: theme.colors.text.primary,
    },
    statusRow: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
    },
    statusBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      minWidth: 80,
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
    },
    textSample: {
      gap: 8,
    },
    primaryText: {
      fontSize: 16,
      color: theme.colors.text.primary,
      fontWeight: '500',
    },
    secondaryText: {
      fontSize: 14,
      color: theme.colors.text.secondary,
    },
    mutedText: {
      fontSize: 12,
      color: theme.colors.text.muted,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Theme Selector */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Theme Selector</Text>
          <View style={styles.card}>
            <ThemeSelector variant="horizontal" showLabels={true} size="medium" />
          </View>
        </View>

        {/* Text Samples */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Typography</Text>
          <View style={[styles.card, styles.textSample]}>
            <Text style={styles.primaryText}>Primary Text - Main content and headings</Text>
            <Text style={styles.secondaryText}>Secondary Text - Supporting information</Text>
            <Text style={styles.mutedText}>Muted Text - Subtle details and placeholders</Text>
          </View>
        </View>

        {/* Buttons */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Buttons</Text>
          <View style={styles.card}>
            <View style={styles.buttonRow}>
              <TouchableOpacity style={styles.primaryButton}>
                <Heart size={16} color={theme.primary.contrast} />
                <Text style={[styles.buttonText, styles.primaryButtonText]}>Primary</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.secondaryButton}>
                <Star size={16} color={theme.colors.text.primary} />
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>Secondary</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Input Field</Text>
          <View style={styles.card}>
            <TextInput
              style={styles.input}
              placeholder="Type something here..."
              placeholderTextColor={theme.colors.text.muted}
              value={inputValue}
              onChangeText={setInputValue}
            />
          </View>
        </View>

        {/* Status Colors */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status Colors</Text>
          <View style={styles.card}>
            <View style={styles.statusRow}>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.success, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.success }]}>Success</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.warning, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.warning }]}>Warning</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.error, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.error }]}>Error</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: withOpacity(theme.status.info, 'light') }]}>
                <Text style={[styles.statusText, { color: theme.status.info }]}>Info</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Journey Colors */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Journey Colors</Text>
          <View style={styles.card}>
            <View style={styles.colorGrid}>
              {journeyColorOptions.map(({ id, name }) => {
                const color = getJourneyColorVariant(id, 'default');
                const isSelected = selectedJourneyColor === id;
                
                return (
                  <TouchableOpacity
                    key={id}
                    onPress={() => setSelectedJourneyColor(id)}
                    accessibilityRole="button"
                    accessibilityLabel={`Select ${name} color`}
                  >
                    <View
                      style={[
                        styles.colorSwatch,
                        { backgroundColor: color },
                        isSelected && styles.selectedColorSwatch,
                      ]}
                    >
                      <Text
                        style={[
                          styles.colorLabel,
                          { color: getJourneyColorVariant(id, 'contrast') },
                        ]}
                      >
                        {name}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </View>

        {/* Selected Journey Color Variants */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{journeyColorOptions.find(c => c.id === selectedJourneyColor)?.name} Variants</Text>
          <View style={styles.card}>
            <View style={styles.colorGrid}>
              {(['default', 'light', 'dark', 'muted'] as const).map((variant) => {
                const color = getJourneyColorVariant(selectedJourneyColor, variant);
                
                return (
                  <View key={variant}>
                    <View
                      style={[
                        styles.colorSwatch,
                        { backgroundColor: color },
                      ]}
                    >
                      <Text
                        style={[
                          styles.colorLabel,
                          { color: getJourneyColorVariant(selectedJourneyColor, 'contrast') },
                        ]}
                      >
                        {variant}
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

export default ThemeShowcase;