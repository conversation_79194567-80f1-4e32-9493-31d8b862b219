import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import SentimentSelector from '../form/SentimentSelector';
import ImpactPicker from '../form/ImpactPicker';
import MilestoneToggle from '../form/MilestoneToggle';
import DatePicker from '../form/DatePicker';
import { Waypoint } from '../../mocks/journeys';

interface WaypointFormProps {
  initialData?: Partial<Waypoint>;
  onSubmit: (data: {
    content: string;
    date: Date;
    sentiment: number;
    impact: 'low' | 'medium' | 'high';
    isMilestone: boolean;
  }) => void;
  buttonText?: string;
  journeyColor: string;
}

export default function WaypointForm({
  initialData,
  onSubmit,
  buttonText = 'Add Waypoint',
  journeyColor
}: WaypointFormProps) {
  const { theme } = useTheme();
  const [content, setContent] = useState(initialData?.content || '');
  const [date, setDate] = useState(initialData?.date || new Date());
  const [sentiment, setSentiment] = useState(initialData?.sentiment || 0);
  const [impact, setImpact] = useState<'low' | 'medium' | 'high'>(initialData?.impact || 'medium');
  const [isMilestone, setIsMilestone] = useState(initialData?.isMilestone || false);
  
  const isValid = content.trim().length > 0;
  const characterCount = content.length;
  const maxCharacters = 280;

  // Update form when initialData changes (for editing)
  useEffect(() => {
    if (initialData) {
      setContent(initialData.content || '');
      setDate(initialData.date || new Date());
      setSentiment(initialData.sentiment || 0);
      setImpact(initialData.impact || 'medium');
      setIsMilestone(initialData.isMilestone || false);
    }
  }, [initialData]);

  const handleSubmit = () => {
    if (!isValid) return;

    onSubmit({
      content: content.trim(),
      date,
      sentiment,
      impact,
      isMilestone
    });
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background.primary
    },
    content: {
      flex: 1,
      padding: 16
    },
    section: {
      marginBottom: 24
    },
    inputContainer: {
      position: 'relative'
    },
    contentInput: {
      borderWidth: 1,
      borderColor: theme.colors.border.secondary,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      color: theme.colors.text.primary,
      backgroundColor: theme.colors.surface.input,
      minHeight: 120,
      textAlignVertical: 'top'
    },
    characterCount: {
      position: 'absolute',
      bottom: 8,
      right: 12,
      fontSize: 12,
      color: theme.colors.text.muted
    },
    mainFormContainer: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 16,
      padding: 20,
      borderWidth: 1,
      borderColor: theme.colors.border.secondary
    },
    label: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 12
    },
    sentimentContainer: {
      alignItems: 'center'
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.border.secondary,
      marginVertical: 20
    },
    formRow: {
      flexDirection: 'row',
      gap: 20
    },
    impactSection: {
      flex: 1
    },
    milestoneSection: {
      flex: 1
    },
    footer: {
      padding: 16,
      paddingBottom: 32,
      backgroundColor: theme.colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border.secondary
    },
    submitButton: {
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
      backgroundColor: journeyColor
    },
    disabledButton: {
      backgroundColor: theme.colors.text.muted,
      opacity: 0.6
    },
    submitButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF'
    }
  }), [theme, journeyColor]);

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.contentInput}
              placeholder="What happened?"
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={maxCharacters}
              placeholderTextColor={theme.colors.text.muted}
            />
            <Text style={styles.characterCount}>
              {characterCount}/{maxCharacters}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <DatePicker
            value={date}
            onChange={setDate}
            journeyColor={journeyColor}
          />
        </View>

        <View style={styles.mainFormContainer}>
          <View style={styles.section}>
            <Text style={styles.label}>How did this feel?</Text>
            <View style={styles.sentimentContainer}>
              <SentimentSelector
                value={sentiment}
                onChange={setSentiment}
                journeyColor={journeyColor}
              />
            </View>
          </View>

          <View style={styles.divider} />

          <View style={styles.formRow}>
            <View style={styles.impactSection}>
              <Text style={styles.label}>Impact</Text>
              <ImpactPicker
                value={impact}
                onChange={setImpact}
                journeyColor={journeyColor}
              />
            </View>

            <View style={styles.milestoneSection}>
              <Text style={styles.label}>Milestone</Text>
              <MilestoneToggle
                value={isMilestone}
                onChange={setIsMilestone}
                journeyColor={journeyColor}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            !isValid && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={!isValid}
        >
          <Text style={styles.submitButtonText}>{buttonText}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
