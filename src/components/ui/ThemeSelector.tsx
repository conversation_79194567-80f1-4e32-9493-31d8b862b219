import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme, useThemeMode } from '../../contexts/ThemeContext';
import { ThemeMode } from '../../types/theme';
import { Sun, Moon, Smartphone } from 'lucide-react-native';

interface ThemeSelectorProps {
  variant?: 'horizontal' | 'vertical';
  showLabels?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export function ThemeSelector({ 
  variant = 'horizontal', 
  showLabels = true, 
  size = 'medium' 
}: ThemeSelectorProps) {
  const { theme } = useTheme();
  const { themeMode, setThemeMode } = useThemeMode();

  const iconSize = size === 'small' ? 16 : size === 'medium' ? 20 : 24;
  const buttonSize = size === 'small' ? 36 : size === 'medium' ? 44 : 52;

  const themeOptions: { mode: ThemeMode; icon: any; label: string }[] = [
    { mode: 'light', icon: Sun, label: 'Light' },
    { mode: 'dark', icon: Moon, label: 'Dark' },
    { mode: 'system', icon: Smartphone, label: 'System' },
  ];

  const styles = StyleSheet.create({
    container: {
      flexDirection: variant === 'horizontal' ? 'row' : 'column',
      alignItems: 'center',
      gap: 8,
    },
    optionContainer: {
      alignItems: 'center',
      gap: 4,
    },
    button: {
      width: buttonSize,
      height: buttonSize,
      borderRadius: buttonSize / 2,
      backgroundColor: theme.colors.surface.card,
      borderWidth: 2,
      borderColor: 'transparent',
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    activeButton: {
      backgroundColor: theme.primary.default,
      borderColor: theme.primary.dark,
    },
    label: {
      fontSize: size === 'small' ? 12 : size === 'medium' ? 14 : 16,
      fontWeight: '500',
      color: theme.colors.text.secondary,
    },
    activeLabel: {
      color: theme.colors.text.primary,
      fontWeight: '600',
    },
  });

  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  return (
    <View style={styles.container}>
      {themeOptions.map(({ mode, icon: Icon, label }) => {
        const isActive = themeMode === mode;
        
        return (
          <View key={mode} style={styles.optionContainer}>
            <TouchableOpacity
              style={[styles.button, isActive && styles.activeButton]}
              onPress={() => handleThemeChange(mode)}
              accessibilityRole="button"
              accessibilityLabel={`Switch to ${label} theme`}
              accessibilityState={{ selected: isActive }}
            >
              <Icon
                size={iconSize}
                color={isActive ? theme.primary.contrast : theme.colors.text.primary}
              />
            </TouchableOpacity>
            {showLabels && (
              <Text style={[styles.label, isActive && styles.activeLabel]}>
                {label}
              </Text>
            )}
          </View>
        );
      })}
    </View>
  );
}

export default ThemeSelector;