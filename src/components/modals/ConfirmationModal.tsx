import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Pressable
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ConfirmationModalProps {
  visible: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  destructive?: boolean;
}

export default function ConfirmationModal({
  visible,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  destructive = false
}: ConfirmationModalProps) {
  const { theme } = useTheme();
  
  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20
    },
    modal: {
      backgroundColor: theme.colors.surface.card,
      borderRadius: 16,
      width: '100%',
      maxWidth: 320,
      shadowColor: theme.colors.text.primary,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 8
    },
    content: {
      padding: 24
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: 8,
      textAlign: 'center'
    },
    message: {
      fontSize: 16,
      color: theme.colors.text.secondary,
      lineHeight: 24,
      textAlign: 'center',
      marginBottom: 24
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 12
    },
    button: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      alignItems: 'center'
    },
    cancelButton: {
      backgroundColor: theme.colors.surface.input,
      borderWidth: 1,
      borderColor: theme.colors.border.primary
    },
    confirmButton: {
      backgroundColor: theme.primary.default
    },
    destructiveButton: {
      backgroundColor: theme.status.error
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text.primary
    },
    confirmButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.primary.contrast
    },
    destructiveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF'
    }
  });
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <Pressable style={styles.overlay} onPress={onCancel}>
        <Pressable style={styles.modal} onPress={(e) => e.stopPropagation()}>
          <View style={styles.content}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.message}>{message}</Text>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onCancel}
              >
                <Text style={styles.cancelButtonText}>{cancelText}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.button,
                  destructive ? styles.destructiveButton : styles.confirmButton
                ]}
                onPress={onConfirm}
              >
                <Text style={[
                  destructive ? styles.destructiveButtonText : styles.confirmButtonText
                ]}>
                  {confirmText}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
}

