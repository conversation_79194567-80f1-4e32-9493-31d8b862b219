import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Theme, ThemeMode, ThemeContextType } from '../types/theme';
import { lightTheme, darkTheme } from '../constants/themes';
import { themeUtils } from '../utils/themeUtils';

const THEME_STORAGE_KEY = 'app_theme_mode_9afaf736';

// Create the context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme Provider Props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme Provider Component
export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [isLoading, setIsLoading] = useState(true);

  // Determine the actual theme based on mode
  const getActualTheme = (mode: ThemeMode): 'light' | 'dark' => {
    if (mode === 'system') {
      return systemColorScheme === 'dark' ? 'dark' : 'light';
    }
    return mode;
  };

  const actualTheme = getActualTheme(themeMode);
  const theme: Theme = actualTheme === 'dark' ? darkTheme : lightTheme;
  const isLightMode = actualTheme === 'light';
  const isDarkMode = actualTheme === 'dark';

  // Load saved theme preference on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
          setThemeModeState(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.warn('Failed to load theme preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  // Save theme preference when it changes
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      setThemeModeState(mode);
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  // Toggle between light and dark (ignores system)
  const toggleTheme = () => {
    const newMode = actualTheme === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
  };

  // Utility functions using themeUtils
  const getFocusStyle = (variant?: keyof typeof theme.focus.alternatives) => {
    return themeUtils.getFocusStyle(theme, variant);
  };

  const getFocusVisibleStyle = (variant?: keyof typeof theme.focus.alternatives) => {
    return themeUtils.getFocusVisibleStyle(theme, variant);
  };

  const getAccessibleTextColor = (backgroundColor: string) => {
    return themeUtils.getAccessibleTextColor(backgroundColor);
  };

  const getJourneyColorVariant = (
    colorId: keyof typeof theme.journey,
    variant: keyof typeof theme.journey.coral
  ) => {
    return theme.journey[colorId][variant];
  };

  const withOpacity = (color: string, opacityLevel: keyof typeof theme.opacity) => {
    return themeUtils.withOpacity(color, theme.opacity[opacityLevel]);
  };

  // Context value
  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    isLightMode,
    isDarkMode,
    toggleTheme,
    setThemeMode,
    getFocusStyle,
    getFocusVisibleStyle,
    getAccessibleTextColor,
    getJourneyColorVariant,
    withOpacity,
  };

  // Don't render until theme is loaded
  if (isLoading) {
    return null;
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme context
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Convenience hooks
export function useThemeMode() {
  const { themeMode, setThemeMode, toggleTheme } = useTheme();
  return { themeMode, setThemeMode, toggleTheme };
}

export function useThemeColors() {
  const { theme } = useTheme();
  return theme;
}

export function useIsDarkMode() {
  const { isDarkMode } = useTheme();
  return isDarkMode;
}

export function useIsLightMode() {
  const { isLightMode } = useTheme();
  return isLightMode;
}
