
import React from 'react';
import { DarkTheme, DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Toaster } from 'sonner-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import MainNavigator from './navigation';

// App Content Component (needs to be inside ThemeProvider)
function AppContent() {
    const { isDarkMode, theme } = useTheme();

    // Create navigation theme based on our theme system
    const navigationTheme = {
        ...(isDarkMode ? DarkTheme : DefaultTheme),
        colors: {
            ...(isDarkMode ? DarkTheme.colors : DefaultTheme.colors),
            primary: theme.primary.default,
            background: theme.colors.background.primary,
            card: theme.colors.surface.card,
            text: theme.colors.text.primary,
            border: theme.colors.border.primary,
            notification: theme.status.error,
        },
    };

    return (
        <SafeAreaProvider>
            <NavigationContainer theme={navigationTheme}>
                <StatusBar
                    style={isDarkMode ? 'light' : 'dark'}
                    backgroundColor={theme.colors.background.primary}
                />
                <Toaster 
                    theme={isDarkMode ? 'dark' : 'light'} 
                    richColors 
                />
                <MainNavigator />
            </NavigationContainer>
        </SafeAreaProvider>
    );
}

// Main App Component
export default function App() {
    return (
        <ThemeProvider>
            <AppContent />
        </ThemeProvider>
    );
}
