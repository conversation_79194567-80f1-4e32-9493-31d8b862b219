import { Theme } from '../types/theme';

/**
 * Theme utility functions for color manipulation and accessibility
 */
export const themeUtils = {
  /**
   * Convert hex color to RGB values
   */
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  /**
   * Calculate luminance of a color (for contrast calculations)
   */
  getLuminance(hex: string): number {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return 0;

    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio(color1: string, color2: string): number {
    const lum1 = this.getLuminance(color1);
    const lum2 = this.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Check if a color combination meets WCAG AA standards
   */
  meetsWCAG(backgroundColor: string, textColor: string, isLargeText = false): boolean {
    const ratio = this.getContrastRatio(backgroundColor, textColor);
    return isLargeText ? ratio >= 3.0 : ratio >= 4.5;
  },

  /**
   * Get accessible text color (black or white) for a given background
   */
  getAccessibleTextColor(backgroundColor: string): string {
    const whiteRatio = this.getContrastRatio(backgroundColor, '#FFFFFF');
    const blackRatio = this.getContrastRatio(backgroundColor, '#000000');
    return whiteRatio > blackRatio ? '#FFFFFF' : '#000000';
  },

  /**
   * Add opacity to a hex color
   */
  withOpacity(hex: string, opacity: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;
    
    const { r, g, b } = rgb;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  /**
   * Lighten a color by a percentage
   */
  lightenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = percent / 100;
    
    const newR = Math.min(255, Math.round(r + (255 - r) * amount));
    const newG = Math.min(255, Math.round(g + (255 - g) * amount));
    const newB = Math.min(255, Math.round(b + (255 - b) * amount));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  },

  /**
   * Darken a color by a percentage
   */
  darkenColor(hex: string, percent: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const { r, g, b } = rgb;
    const amount = percent / 100;
    
    const newR = Math.max(0, Math.round(r * (1 - amount)));
    const newG = Math.max(0, Math.round(g * (1 - amount)));
    const newB = Math.max(0, Math.round(b * (1 - amount)));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  },

  /**
   * Generate focus style object
   */
  getFocusStyle(theme: Theme, variant?: keyof typeof theme.focus.alternatives): object {
    const isDark = theme.mode === 'dark';
    const ringColor = variant 
      ? theme.focus.alternatives[variant]
      : (isDark ? theme.focus.ring.colorDark : theme.focus.ring.color);
    
    return {
      outline: 'none',
      shadowColor: ringColor,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 1,
      shadowRadius: parseInt(theme.focus.ring.width),
      elevation: 0, // Android
    };
  },

  /**
   * Generate focus-visible style (keyboard only)
   */
  getFocusVisibleStyle(theme: Theme, variant?: keyof typeof theme.focus.alternatives): object {
    return {
      // This would be handled by the component's focus state
      // React Native doesn't have :focus-visible, so components need to track keyboard vs touch
      ...this.getFocusStyle(theme, variant),
    };
  },

  /**
   * Generate button styles with theme
   */
  getButtonStyles(theme: Theme, variant: 'primary' | 'secondary' = 'primary') {
    if (variant === 'primary') {
      return {
        backgroundColor: theme.primary.default,
        color: theme.primary.contrast,
        borderColor: 'transparent',
      };
    }
    
    return {
      backgroundColor: 'transparent',
      color: theme.colors.text.primary,
      borderColor: theme.colors.border.primary,
      borderWidth: 1,
    };
  },

  /**
   * Generate input styles with theme
   */
  getInputStyles(theme: Theme) {
    return {
      backgroundColor: theme.colors.surface.input,
      borderColor: theme.colors.border.primary,
      color: theme.colors.text.primary,
      placeholderTextColor: theme.colors.text.muted,
    };
  },

  /**
   * Generate card styles with theme
   */
  getCardStyles(theme: Theme) {
    return {
      backgroundColor: theme.colors.surface.card,
      borderColor: theme.colors.border.secondary,
      shadowColor: theme.mode === 'dark' ? '#000000' : '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: theme.mode === 'dark' ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 2, // Android
    };
  },

  /**
   * Validate theme accessibility
   */
  validateThemeAccessibility(theme: Theme): { [key: string]: boolean } {
    const results: { [key: string]: boolean } = {};
    
    // Check primary color accessibility
    results.primaryOnBackground = this.meetsWCAG(
      theme.colors.background.primary,
      theme.primary.default
    );
    
    results.textOnBackground = this.meetsWCAG(
      theme.colors.background.primary,
      theme.colors.text.primary
    );
    
    results.textOnPrimary = this.meetsWCAG(
      theme.primary.default,
      theme.primary.contrast
    );
    
    // Check focus ring accessibility
    results.focusOnBackground = this.getContrastRatio(
      theme.colors.background.primary,
      theme.focus.ring.color
    ) >= 3.0;
    
    return results;
  },
};
