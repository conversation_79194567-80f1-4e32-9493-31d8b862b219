import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { themeUtils } from '../utils/themeUtils';

/**
 * Enhanced hook for creating theme-aware styles
 * Provides common style patterns and utilities
 */
export function useThemeStyles() {
  const { theme } = useTheme();

  const styles = useMemo(() => {
    return StyleSheet.create({
      // Container styles
      container: {
        flex: 1,
        backgroundColor: theme.colors.background.primary,
      },
      safeContainer: {
        flex: 1,
        backgroundColor: theme.colors.background.primary,
        paddingHorizontal: 20,
      },
      centeredContainer: {
        flex: 1,
        backgroundColor: theme.colors.background.primary,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 20,
      },

      // Card styles
      card: themeUtils.getCardStyles(theme),
      elevatedCard: {
        ...themeUtils.getCardStyles(theme),
        shadowOpacity: theme.mode === 'dark' ? 0.4 : 0.15,
        shadowRadius: 8,
        elevation: 4,
      },

      // Button styles
      primaryButton: {
        ...themeUtils.getButtonStyles(theme, 'primary'),
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
      },
      secondaryButton: {
        ...themeUtils.getButtonStyles(theme, 'secondary'),
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
      },
      roundButton: {
        ...themeUtils.getButtonStyles(theme, 'primary'),
        width: 48,
        height: 48,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
      },

      // Input styles
      input: {
        ...themeUtils.getInputStyles(theme),
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        borderWidth: 1,
        fontSize: 16,
      },
      searchInput: {
        ...themeUtils.getInputStyles(theme),
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 20,
        borderWidth: 1,
        fontSize: 14,
      },

      // Text styles
      heading1: {
        fontSize: 28,
        fontWeight: '700',
        color: theme.colors.text.primary,
        lineHeight: 34,
      },
      heading2: {
        fontSize: 24,
        fontWeight: '600',
        color: theme.colors.text.primary,
        lineHeight: 30,
      },
      heading3: {
        fontSize: 20,
        fontWeight: '600',
        color: theme.colors.text.primary,
        lineHeight: 26,
      },
      bodyLarge: {
        fontSize: 16,
        fontWeight: '400',
        color: theme.colors.text.primary,
        lineHeight: 22,
      },
      bodyMedium: {
        fontSize: 14,
        fontWeight: '400',
        color: theme.colors.text.secondary,
        lineHeight: 20,
      },
      bodySmall: {
        fontSize: 12,
        fontWeight: '400',
        color: theme.colors.text.muted,
        lineHeight: 16,
      },
      caption: {
        fontSize: 10,
        fontWeight: '500',
        color: theme.colors.text.muted,
        lineHeight: 14,
        textTransform: 'uppercase',
        letterSpacing: 0.5,
      },

      // Layout styles
      row: {
        flexDirection: 'row',
        alignItems: 'center',
      },
      rowBetween: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
      column: {
        flexDirection: 'column',
      },
      center: {
        alignItems: 'center',
        justifyContent: 'center',
      },

      // Spacing
      marginSmall: { margin: 8 },
      marginMedium: { margin: 16 },
      marginLarge: { margin: 24 },
      paddingSmall: { padding: 8 },
      paddingMedium: { padding: 16 },
      paddingLarge: { padding: 24 },

      // Borders
      borderTop: {
        borderTopWidth: 1,
        borderTopColor: theme.colors.border.secondary,
      },
      borderBottom: {
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.border.secondary,
      },
      borderFull: {
        borderWidth: 1,
        borderColor: theme.colors.border.primary,
      },

      // Shadows
      shadowSmall: {
        shadowColor: theme.colors.text.primary,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: theme.mode === 'dark' ? 0.3 : 0.1,
        shadowRadius: 2,
        elevation: 1,
      },
      shadowMedium: {
        shadowColor: theme.colors.text.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: theme.mode === 'dark' ? 0.3 : 0.1,
        shadowRadius: 4,
        elevation: 2,
      },
      shadowLarge: {
        shadowColor: theme.colors.text.primary,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: theme.mode === 'dark' ? 0.4 : 0.15,
        shadowRadius: 8,
        elevation: 4,
      },
    });
  }, [theme]);

  // Utility functions
  const utils = useMemo(() => ({
    // Get journey color with variant
    getJourneyColor: (colorId: keyof typeof theme.journey, variant: keyof typeof theme.journey.coral = 'default') => {
      return theme.journey[colorId][variant];
    },

    // Add opacity to any color
    withOpacity: (color: string, opacity: keyof typeof theme.opacity) => {
      return themeUtils.withOpacity(color, theme.opacity[opacity]);
    },

    // Get accessible text color for background
    getTextColor: (backgroundColor: string) => {
      return themeUtils.getAccessibleTextColor(backgroundColor);
    },

    // Get focus styles
    getFocusStyle: (variant?: keyof typeof theme.focus.alternatives) => {
      return themeUtils.getFocusStyle(theme, variant);
    },

    // Create custom button style
    createButtonStyle: (backgroundColor: string, textColor?: string) => ({
      backgroundColor,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...(textColor && { color: textColor }),
    }),

    // Create status badge style
    createStatusBadge: (status: keyof typeof theme.status) => ({
      backgroundColor: themeUtils.withOpacity(theme.status[status], theme.opacity.light),
      borderWidth: 1,
      borderColor: theme.status[status],
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    }),
  }), [theme]);

  return { styles, utils, theme };
}

export default useThemeStyles;